<x-app-layout>



    <!-- Hero Section with Romantic Background -->
    <section class="relative overflow-hidden py-12 lg:py-16" style="background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.1) 50%, rgba(236, 72, 153, 0.1) 100%)">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-indigo-200 to-pink-200 rounded-full opacity-20 animate-pulse"></div>
            <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-br from-purple-200 to-indigo-200 rounded-full opacity-20 animate-pulse" style="animation-delay: 1s;"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Back Button -->
            <div class="mb-6">
                <a href="{{ route('find-person.index') }}" class="inline-flex items-center px-5 py-2 bg-white/80 backdrop-blur-md hover:bg-white/90 text-gray-700 text-sm font-medium rounded-full border border-white/50 hover:border-indigo-300 transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-indigo-500/20">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    Back to Find Person
                </a>
            </div>

            <!-- Hero Content -->
            <div class="text-center">
                @if(isset($fromHireRequest))
                    <!-- Hire Request Context Notice -->
                    <div class="mb-4 inline-flex items-center px-4 py-2 bg-blue-100 border border-blue-300 rounded-full text-blue-800 text-sm font-medium">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Viewing profile from hire request - Full profile access granted
                    </div>
                @endif

                @if(isset($mutualRejectionStatus) && $mutualRejectionStatus && $mutualRejectionStatus['any_rejection'])
                    <!-- Sugar Partner Mutual Rejection Status Notice -->
                    @if($mutualRejectionStatus['mutual_rejection'])
                        <!-- Both users rejected each other -->
                        <div class="mb-4 inline-flex items-center px-4 py-2 bg-red-100 border border-red-300 text-red-800 rounded-full text-sm font-medium">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                            </svg>
                            @if($mutualRejectionStatus['you_rejected_them'] === 'hard_reject' || $mutualRejectionStatus['they_rejected_you'] === 'hard_reject')
                                Hard rejected each other
                            @else
                                Soft rejected each other
                            @endif
                        </div>
                    @elseif($mutualRejectionStatus['you_rejected_them'])
                        <!-- Current user rejected the other user -->
                        <div class="mb-4 inline-flex items-center px-4 py-2 {{ $mutualRejectionStatus['you_rejected_them'] === 'hard_reject' ? 'bg-red-100 border border-red-300 text-red-800' : 'bg-yellow-100 border border-yellow-300 text-yellow-800' }} rounded-full text-sm font-medium">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                @if($mutualRejectionStatus['you_rejected_them'] === 'hard_reject')
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                @else
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                @endif
                            </svg>
                            You {{ $mutualRejectionStatus['you_rejected_them'] === 'hard_reject' ? 'hard' : 'soft' }} rejected this user previously
                        </div>
                    @elseif($mutualRejectionStatus['they_rejected_you'])
                        <!-- Other user rejected the current user -->
                        <div class="mb-4 inline-flex items-center px-4 py-2 {{ $mutualRejectionStatus['they_rejected_you'] === 'hard_reject' ? 'bg-red-100 border border-red-300 text-red-800' : 'bg-yellow-100 border border-yellow-300 text-yellow-800' }} rounded-full text-sm font-medium">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                @if($mutualRejectionStatus['they_rejected_you'] === 'hard_reject')
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                @else
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                @endif
                            </svg>
                            You {{ $mutualRejectionStatus['they_rejected_you'] === 'hard_reject' ? 'hard' : 'soft' }} rejected by this user previously
                        </div>
                    @endif
                @endif
                <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 leading-tight">
                    <span class="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                        {{ $user->name }}
                    </span>
                </h1>
                <p class="text-lg md:text-xl text-gray-700 max-w-2xl mx-auto leading-relaxed font-light">
                    Professional time spending services with verified quality
                </p>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

            <!-- User Detail Card -->
            <div class="bg-white/80 backdrop-blur-md rounded-2xl shadow-xl border border-white/50 overflow-hidden mb-8 relative hover:shadow-2xl hover:shadow-indigo-500/10 transition-all duration-300">
                <!-- Action Buttons - Top Right Corner -->
                @if(!isset($fromHireRequest))
                <div class="absolute top-4 right-4 z-10 flex gap-2">
                    <button id="hireMeButton" onclick="scrollToBookingForm()" class="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-bold py-2 px-5 rounded-full border-0 transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-purple-500/30 hover:brightness-110">
                        <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Hire Me
                    </button>
                    @auth
                        @if(auth()->id() !== $user->id)
                            <button onclick="openReportModal({{ $user->id }}, '{{ addslashes($user->name) }}')" class="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium py-2 px-4 rounded-full border-0 transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-red-500/30 hover:brightness-110" title="Report User">
                                <svg class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9"></path>
                                </svg>
                                Report
                            </button>
                        @endif
                    @endauth
                </div>
                @endif

                <!-- Active Bookings Display - Below Hire Me Button -->
                <div id="activeBookingsSection" class="absolute top-16 right-4 z-10 hidden">
                    <div class="bg-white/95 backdrop-blur-md rounded-xl p-4 border border-blue-200 shadow-lg max-w-sm">
                        <div class="flex items-center mb-3">
                            <svg class="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="font-semibold text-gray-800 text-sm">Your Active Bookings</span>
                        </div>
                        <div id="activeBookingsList" class="space-y-2"></div>
                    </div>
                </div>

                <!-- Accepted Booking Status Display -->
                <div id="acceptedBookingStatus" class="absolute top-16 right-4 z-10 hidden">
                    <div class="bg-green-50/95 backdrop-blur-md rounded-xl p-4 border border-green-200 shadow-lg max-w-sm"
                         style="background-color: rgba(240, 253, 244, 0.95); border-color: #bbf7d0; box-shadow: 0 10px 25px rgba(34, 197, 94, 0.2);">
                        <div class="flex items-center mb-3">
                            <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="font-semibold text-green-800 text-sm">Booking Confirmed</span>
                        </div>
                        <div id="acceptedBookingDetails" class="space-y-2"></div>
                        <button onclick="openChatFromProfile()"
                                class="w-full mt-3 px-3 py-2 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                                style="background-color: #2563eb; color: white; padding: 8px 12px; font-size: 12px; font-weight: 500; border-radius: 8px; border: none; cursor: pointer; transition: all 0.2s;"
                                onmouseover="this.style.backgroundColor='#1d4ed8'"
                                onmouseout="this.style.backgroundColor='#2563eb'">
                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            Start Chat
                        </button>
                    </div>
                </div>



                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <!-- Left Column - Profile Image and Basic Info -->
                        <div class="lg:col-span-1">
                            <!-- Profile Image -->
                            <div class="flex justify-center mb-6">
                                <div class="w-48 h-48 rounded-full overflow-hidden border-4 border-indigo-200 shadow-xl cursor-pointer hover:shadow-2xl hover:shadow-indigo-500/20 transition-all duration-300 hover:border-purple-300 relative" onclick="openProfileImage()">
                                    @if($user->profile_picture)
                                        <img src="{{ asset('storage/' . $user->profile_picture) }}"
                                             alt="{{ $user->name }}"
                                             class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                                             loading="lazy"
                                             onerror="this.src='{{ asset('images/default-avatar.png') }}';">
                                    @elseif($user->galleryImages->count() > 0)
                                        <img src="{{ $user->galleryImages->first()->image_url }}"
                                             alt="{{ $user->name }}"
                                             class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                                             loading="lazy"
                                             onerror="this.src='{{ asset('images/default-avatar.png') }}';">
                                    @else
                                        <img src="{{ asset('images/default-avatar.png') }}"
                                             alt="{{ $user->name }}"
                                             class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                                    @endif
                                </div>
                            </div>

                            <!-- Basic Info -->
                            <div class="text-center">
                                <h2 class="text-2xl font-bold mb-3">
                                    <span class="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                                        {{ $user->name }}
                                    </span>
                                </h2>

                                @if(\App\Helpers\FeatureHelper::isRatingReviewSystemActive())
                                    @php
                                        $averageRating = $user->getAverageRating();
                                        $totalReviews = $user->getTotalReviewsCount();
                                    @endphp
                                    @if($totalReviews > 0)
                                        <div class="flex items-center justify-center mb-3">
                                            <div class="flex items-center space-x-2">
                                                <div class="flex">
                                                    {!! $user->getStarRatingHtml() !!}
                                                </div>
                                                <span class="text-lg font-semibold text-gray-900">{{ number_format($averageRating, 1) }}</span>
                                                <span class="text-gray-600">({{ $totalReviews }} review{{ $totalReviews > 1 ? 's' : '' }})</span>
                                            </div>
                                        </div>
                                    @endif
                                @endif

                                <!-- Gender Badge -->
                                <div class="flex justify-center mb-4">
                                    <span class="inline-block px-3 py-1 text-sm font-semibold rounded-full {{ $user->gender === 'male' ? 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200' : 'bg-gradient-to-r from-pink-100 to-purple-100 text-pink-800 border border-pink-200' }} shadow-sm">
                                        {{ ucfirst($user->gender) }}
                                    </span>
                                </div>

                                <!-- Hourly Rate -->
                                @if(!isset($fromHireRequest))
                                <div class="bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 rounded-xl p-4 mb-6 border border-indigo-100 shadow-md">
                                    <div class="text-center">
                                        <p class="text-sm text-gray-600 mb-1 font-medium">Hourly Rate</p>
                                        <p class="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">₹{{ number_format($user->hourly_rate, 0) }}/h</p>
                                    </div>
                                </div>
                                @endif



                                <!-- Contact Actions - Removed as requested -->

                                <!-- Date of Birth (if visible) -->
                                @if(($user->show_date_of_birth || isset($fromHireRequest)) && $user->date_of_birth)
                                    <div class="bg-gradient-to-br from-gray-50 to-indigo-50 rounded-xl p-4 border border-gray-100 shadow-sm">
                                        <!-- Debug info (remove after testing) -->
                                        <small class="text-red-500">DEBUG: hide_dob_year = {{ $user->hide_dob_year ? 'true' : 'false' }}</small><br>

                                        <p class="text-sm text-gray-600 mb-1 font-medium">
                                            @if($user->hide_dob_year)
                                                Date of Birth
                                            @else
                                                Date of Birth & Age
                                            @endif
                                        </p>
                                        <p class="font-bold text-gray-900">
                                            @if($user->hide_dob_year)
                                                {{ $user->date_of_birth->format('F d') }}
                                            @else
                                                {{ $user->date_of_birth->format('F d, Y') }} ({{ \Carbon\Carbon::parse($user->date_of_birth)->age }} years old)
                                            @endif
                                        </p>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <!-- Right Column - Detailed Information -->
                        <div class="lg:col-span-2">
                            <!-- Interests Section -->
                            @if(($user->show_interests_hobbies || isset($fromHireRequest)) && $user->interests)
                                <div class="mb-6">
                                    <h2 class="text-xl font-bold mb-4">
                                        <span class="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                                            Interests & Hobbies
                                        </span>
                                    </h2>
                                    <div class="flex flex-wrap gap-2">
                                        @foreach(explode(',', $user->interests) as $interest)
                                            <span class="inline-block px-3 py-2 text-sm font-semibold rounded-full bg-gradient-to-r from-indigo-100 via-purple-100 to-pink-100 text-gray-800 border border-indigo-200 shadow-sm hover:shadow-md hover:border-purple-300 transition-all duration-300">
                                                {{ trim($interest) }}
                                            </span>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <!-- Expectations Section -->
                            @if(($user->show_expectations || isset($fromHireRequest)) && $user->expectation)
                                <div class="mb-6">
                                    <h2 class="text-xl font-bold mb-4">
                                        <span class="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                                            Expectations
                                        </span>
                                    </h2>
                                    <div class="bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 rounded-xl p-5 border border-indigo-100 shadow-md">
                                        <p class="text-gray-700 leading-relaxed">{{ $user->expectation }}</p>
                                    </div>
                                </div>
                            @endif

                            <!-- Gallery Section -->
                            @if(($user->show_gallery_images || isset($fromHireRequest)) && $user->galleryImages->count() > 0)
                                <div class="mb-6">
                                    <h2 class="text-xl font-bold mb-4">
                                        <span class="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                                            Gallery ({{ $user->galleryImages->count() }} {{ $user->galleryImages->count() == 1 ? 'image' : 'images' }})
                                        </span>
                                    </h2>

                                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                                        @foreach($user->galleryImages as $index => $image)
                                            <div class="relative w-full h-48 rounded-xl overflow-hidden border-2 border-indigo-100 shadow-md hover:shadow-xl hover:shadow-indigo-500/20 transition-all duration-300 cursor-pointer group hover:border-purple-300" onclick="openGalleryImage({{ $index }})">
                                                <img src="{{ $image->image_url }}"
                                                     alt="Gallery image {{ $index + 1 }}"
                                                     class="w-full h-full object-cover group-hover:brightness-110 transition-all duration-300"
                                                     loading="lazy"
                                                     onerror="this.style.display='none'; this.parentElement.style.backgroundColor='#f3f4f6'; this.parentElement.innerHTML='<div class=\'flex items-center justify-center h-full\'><svg class=\'w-8 h-8 text-gray-400\' fill=\'currentColor\' viewBox=\'0 0 20 20\'><path fill-rule=\'evenodd\' d=\'M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z\' clip-rule=\'evenodd\'></path></svg></div>'">
                                                <!-- Image number overlay -->
                                                <div class="absolute top-2 left-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-xs px-2 py-1 rounded-full font-semibold shadow-lg">
                                                    {{ $index + 1 }}
                                                </div>
                                                <!-- Hover overlay -->
                                                <div class="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                                                    <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                                        <svg class="w-8 h-8 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @else
                                @if($user->show_gallery_images || isset($fromHireRequest))
                                    <div class="mb-8">
                                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Gallery</h2>
                                        <div class="text-center py-8 text-gray-500">
                                            <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                            <p>No gallery images available</p>
                                        </div>
                                    </div>
                                @endif
                            @endif


                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabs Section -->
            @if(!isset($fromHireRequest))
            <div id="userDetailTabs" class="mb-8">
                <!-- Tab Navigation -->
                <div class="bg-white/80 backdrop-blur-md rounded-xl shadow-lg border border-white/50 p-1 mb-6">
                    <nav class="flex space-x-1" aria-label="Tabs">
                        <button onclick="switchUserTab('booking')"
                                id="booking-tab"
                                class="user-tab-button flex-1 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg py-3 px-4 font-semibold text-sm transition-all duration-300 shadow-md hover:shadow-lg hover:brightness-110">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Book Time
                        </button>
                        <button onclick="switchUserTab('bookings')"
                                id="bookings-tab"
                                class="user-tab-button flex-1 bg-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg py-3 px-4 font-semibold text-sm transition-all duration-300">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 00-2 2"></path>
                            </svg>
                            My Bookings
                        </button>
                        @if(\App\Helpers\FeatureHelper::isRatingReviewSystemActive())
                            <button onclick="switchUserTab('reviews')"
                                    id="reviews-tab"
                                    class="user-tab-button flex-1 bg-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg py-3 px-4 font-semibold text-sm transition-all duration-300">
                                <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                                Reviews ({{ $user->getTotalReviewsCount() }})
                            </button>
                        @endif
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="tab-content-container">
                    <!-- Booking Tab (Default) -->
                    <div id="booking-content" class="user-tab-content">
                        <div id="bookingFormSection" class="booking-form-container">
                            <div class="bg-white/80 backdrop-blur-md rounded-3xl shadow-2xl border border-white/50 overflow-hidden">
                                <!-- Header -->
                                <div class="bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50 p-5">
                                    <div class="text-center">
                                        <div class="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl mx-auto mb-4 flex items-center justify-center shadow-lg">
                                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                        <h2 class="text-2xl font-bold mb-2">
                                            <span class="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                                                Book Appointment with {{ $user->name }}
                                            </span>
                                        </h2>
                                        <p class="text-gray-600">Schedule your time and complete the booking</p>
                                    </div>
                                </div>

                    <!-- Form Content -->
                    <div class="p-6">
                        <form id="bookingForm" class="max-w-3xl mx-auto space-y-5">
                            @csrf
                            <input type="hidden" name="provider_id" value="{{ $user->id }}">

                            <!-- Meeting Location (Fixed to Provider's Location) -->
                            <div class="space-y-3">
                                <label class="block text-base font-bold text-gray-800">
                                    <svg class="w-4 h-4 inline mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    Meeting Location
                                </label>
                                <div class="bg-gradient-to-br from-indigo-50 to-purple-50 border-2 border-indigo-200 rounded-xl p-4 shadow-sm">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <p class="text-lg font-bold text-indigo-800">{{ $user->service_location ?: 'Location not specified' }}</p>
                                            <p class="text-sm text-indigo-600">You will meet {{ $user->name }} at this location</p>
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" name="location" value="{{ $user->service_location }}">
                                <div class="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg p-3">
                                    <div class="flex items-start space-x-2">
                                        <svg class="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                        <p class="text-sm text-amber-800 font-medium">
                                            <strong>Important:</strong> {{ $user->name }} provides services only at their location. You will need to travel to {{ $user->service_location }} for this appointment.
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Date Selection -->
                            <div class="space-y-3">
                                <label class="block text-base font-bold text-gray-800">
                                    <svg class="w-4 h-4 inline mr-2 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Select Date
                                </label>
                                <div class="date-picker-container">
                                    <input type="date" id="booking_date" name="booking_date"
                                           class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-0 transition-all duration-200 text-gray-900 font-medium shadow-sm hover:shadow-md"
                                           min="{{ date('Y-m-d') }}"
                                           required
                                           placeholder="Select booking date">
                                </div>
                            </div>

                            <!-- Time Selection -->
                            <div class="space-y-3">
                                <label class="block text-base font-bold text-gray-800">
                                    <svg class="w-4 h-4 inline mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Available Time Slots
                                </label>

                                <!-- Available Times Display -->
                                <div id="availableTimesContainer" class="hidden">
                                    <div class="bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 rounded-xl p-4 mb-4 shadow-sm">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span class="text-base font-bold text-green-800">Available on <span id="selectedDateDisplay"></span></span>
                                        </div>
                                        <div class="text-sm text-green-700">
                                            <span class="font-semibold">Available Hours:</span>
                                            <span id="availableHoursDisplay" class="font-medium"></span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Holiday Message -->
                                <div id="holidayMessage" class="hidden">
                                    <div class="bg-gradient-to-r from-red-50 to-pink-50 border-2 border-red-200 rounded-xl p-4 shadow-sm">
                                        <div class="flex items-center space-x-2">
                                            <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636"></path>
                                            </svg>
                                            <span class="text-base font-bold text-red-800">{{ $user->name }} is not available on this date (Holiday)</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- No Schedule Message -->
                                <div id="noScheduleMessage" class="hidden">
                                    <div class="bg-gradient-to-r from-gray-50 to-slate-50 border-2 border-gray-200 rounded-xl p-4 shadow-sm">
                                        <div class="flex items-center space-x-2">
                                            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span class="text-base font-bold text-gray-700">Please select a date first to see available time slots</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Time Range Inputs (Hidden until date is selected) -->
                                <div id="timeInputsContainer" class="hidden">
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-bold text-gray-700 mb-1">Start Time</label>
                                            <div class="relative">
                                                <input type="text" id="start_time" name="start_time"
                                                       class="w-full px-3 py-2 border-2 border-gray-200 rounded-lg focus:border-purple-500 focus:ring-0 transition-all duration-200 text-gray-900 font-medium shadow-sm hover:shadow-md"
                                                       placeholder="Search or select start time"
                                                       autocomplete="off"
                                                       spellcheck="false">
                                                <input type="hidden" id="start_time_value" name="start_time_value">
                                                <div id="start_time_dropdown" class="absolute z-50 w-full bg-white border border-gray-200 rounded-lg shadow-lg mt-1 max-h-60 overflow-y-auto hidden">
                                                    <!-- Options will be populated by JavaScript -->
                                                </div>
                                                <!-- Dropdown arrow icon -->
                                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-bold text-gray-700 mb-1">End Time</label>
                                            <div class="relative">
                                                <input type="text" id="end_time" name="end_time"
                                                       class="w-full px-3 py-2 border-2 border-gray-200 rounded-lg focus:border-indigo-500 focus:ring-0 transition-all duration-200 text-gray-900 font-medium shadow-sm hover:shadow-md"
                                                       placeholder="Search or select end time"
                                                       autocomplete="off"
                                                       spellcheck="false">
                                                <input type="hidden" id="end_time_value" name="end_time_value">
                                                <div id="end_time_dropdown" class="absolute z-50 w-full bg-white border border-gray-200 rounded-lg shadow-lg mt-1 max-h-60 overflow-y-auto hidden">
                                                    <!-- Options will be populated by JavaScript -->
                                                </div>
                                                <!-- Dropdown arrow icon -->
                                                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Duration Display -->
                            <div id="durationDisplay" class="hidden">
                                <div class="bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-50 border-2 border-purple-200 rounded-xl p-4 shadow-sm">
                                    <div class="flex items-center justify-between">
                                        <span class="text-base font-bold text-gray-800 flex items-center">
                                            <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                            </svg>
                                            Duration
                                        </span>
                                        <span id="calculatedDuration" class="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">0 hours</span>
                                    </div>
                                    <!-- Billing Info -->
                                    <div id="billingInfo" class="hidden mt-3"></div>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="space-y-3">
                                <label class="block text-base font-bold text-gray-800">
                                    <svg class="w-4 h-4 inline mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                    Notes (Optional)
                                </label>
                                <textarea id="notes" name="notes" rows="3"
                                          class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-0 transition-all duration-200 resize-none text-gray-900 shadow-sm hover:shadow-md"
                                          placeholder="Any special requests or preferences..."></textarea>
                            </div>

                            <!-- Payment Breakdown Display -->
                            <div id="paymentBreakdownDisplay" class="hidden mt-2 pt-2 border-t border-purple-200">
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="text-base font-semibold text-gray-800 mb-2">Payment Breakdown:</div>
                                    <table class="w-full text-sm">
                                        <tr class="border-b border-gray-200">
                                            <td class="py-2 text-gray-600">Amount</td>
                                            <td id="breakdownAmount" class="py-2 text-right font-semibold">₹0</td>
                                        </tr>
                                        <tr id="platformFeeRow" class="border-b border-gray-200 hidden">
                                            <td class="py-2 text-gray-600">Platform fee</td>
                                            <td id="breakdownPlatformFee" class="py-2 text-right text-gray-600">₹0</td>
                                        </tr>
                                        <tr id="walletBalanceRow" class="border-b border-gray-200 hidden">
                                            <td class="py-2 text-gray-600">Wallet Balance</td>
                                            <td id="breakdownWallet" class="py-2 text-right text-green-600">-₹0</td>
                                        </tr>
                                        <tr id="needToPayOnlineRow" class="border-b border-gray-200 hidden">
                                            <td class="py-2 text-gray-600">Need to pay online</td>
                                            <td id="breakdownOnline" class="py-2 text-right text-red-600">₹0</td>
                                        </tr>
                                        <tr id="creditToWalletRow" class="border-b border-gray-200 hidden">
                                            <td class="py-2 text-gray-600">Credit to wallet</td>
                                            <td id="breakdownCredit" class="py-2 text-right text-gray-600">₹0</td>
                                        </tr>
                                        <tr class="border-t-2 border-gray-800 font-semibold">
                                            <td class="py-2 text-gray-800">Final Total</td>
                                            <td id="breakdownFinal" class="py-2 text-right text-lg text-red-600">₹0</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Book Now Button -->
                            <button type="submit" id="bookNowButton"
                                    class="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-blue-500/25 hover:brightness-110 disabled:opacity-50 disabled:cursor-not-allowed">
                                <span id="bookNowButtonContent" class="flex items-center justify-center space-x-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    <span id="bookNowButtonText">Book Now & Pay</span>
                                </span>
                                <span id="bookNowButtonLoading" class="hidden flex items-center justify-center space-x-2">
                                    <svg class="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    <span id="bookNowButtonLoadingText">Creating Booking...</span>
                                </span>
                            </button>
                        </form>

                        <!-- Active Bookings in Form Section -->
                        <div id="activeBookingsFormSection" class="hidden mt-8">
                            <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200">
                                <div class="flex items-center mb-4">
                                    <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 00-2 2m-6 9l2 2 4-4"></path>
                                    </svg>
                                    <span class="font-semibold text-gray-800">Your Active Bookings with {{ $user->name }}</span>
                                </div>
                                <div id="activeBookingsFormList" class="space-y-4"></div>
                            </div>
                        </div>
                    </div>
                </div>
                        </div>
                    </div>

                    <!-- Bookings Tab -->
                    <div id="bookings-content" class="user-tab-content hidden">
                        <div class="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
                            <!-- Header -->
                            <div class="bg-gradient-to-br from-pink-50 via-blue-50 to-purple-50 p-6">
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl mx-auto mb-4 flex items-center justify-center">
                                        <svg class="w-8 h-8 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 00-2 2"></path>
                                        </svg>
                                    </div>
                                    <h2 class="text-2xl font-bold mb-2 text-gray-800">My Bookings with {{ $user->name }}</h2>
                                    <p class="text-gray-600">View and manage your booking history</p>
                                </div>
                            </div>

                            <!-- Bookings Content -->
                            <div class="p-8">
                                <div id="userBookingsContainer" class="space-y-4">
                                    <!-- Bookings will be loaded here via JavaScript -->
                                    <div class="text-center py-8">
                                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                                        <p class="text-gray-600 mt-2">Loading your bookings...</p>
                                    </div>
                                </div>

                                <!-- Load More Bookings Button -->
                                <div id="loadMoreBookingsContainer" class="text-center mt-4 hidden">
                                    <button onclick="loadMoreBookings()" id="loadMoreBookingsBtn" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors">
                                        Load More
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reviews Tab -->
                    @if(\App\Helpers\FeatureHelper::isRatingReviewSystemActive())
                        <div id="reviews-content" class="user-tab-content hidden">
                            <div class="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
                                <!-- Header -->
                                <div class="bg-gradient-to-br from-pink-50 via-blue-50 to-purple-50 p-6">
                                    <div class="text-center">
                                        <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl mx-auto mb-4 flex items-center justify-center">
                                            <svg class="w-8 h-8 text-gray-700" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                            </svg>
                                        </div>
                                        <h2 class="text-2xl font-bold mb-2 text-gray-800">Reviews for {{ $user->name }}</h2>
                                        <p class="text-gray-600">See what others are saying</p>
                                    </div>
                                </div>

                                <!-- Reviews Content -->
                                <div class="p-8">
                                    @php
                                        $averageRating = $user->getAverageRating();
                                        $totalReviews = $user->getTotalReviewsCount();
                                        $ratingDistribution = $user->getRatingDistribution();
                                    @endphp

                                    @if($totalReviews > 0)
                                        <!-- Rating Summary Card -->
                                        <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-6 border border-yellow-200 mb-6">
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <!-- Overall Rating -->
                                                <div class="text-center">
                                                    <div class="text-4xl font-bold text-gray-900 mb-2">{{ number_format($averageRating, 1) }}</div>
                                                    <div class="flex justify-center mb-2">
                                                        {!! $user->getStarRatingHtml() !!}
                                                    </div>
                                                    <p class="text-gray-600">Based on {{ $totalReviews }} review{{ $totalReviews > 1 ? 's' : '' }}</p>
                                                </div>

                                                <!-- Rating Distribution -->
                                                <div class="space-y-2">
                                                    @for($i = 5; $i >= 1; $i--)
                                                        @php
                                                            $count = $ratingDistribution[$i] ?? 0;
                                                            $percentage = $totalReviews > 0 ? ($count / $totalReviews) * 100 : 0;
                                                        @endphp
                                                        <div class="flex items-center space-x-3">
                                                            <span class="text-sm font-medium text-gray-700 w-8">{{ $i }} ★</span>
                                                            <div class="flex-1 bg-gray-200 rounded-full h-2">
                                                                <div class="bg-yellow-400 h-2 rounded-full transition-all duration-500" style="width: {{ $percentage }}%"></div>
                                                            </div>
                                                            <span class="text-sm text-gray-600 w-8">{{ $count }}</span>
                                                        </div>
                                                    @endfor
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Reviews List -->
                                        <div class="space-y-4" id="reviewsTabList">
                                            <div id="userReviewsTabContainer" class="space-y-4">
                                                <!-- Reviews will be loaded here via JavaScript -->
                                                <div class="text-center py-8">
                                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
                                                    <p class="text-gray-600 mt-2">Loading reviews...</p>
                                                </div>
                                            </div>

                                            <!-- Load More Button -->
                                            <div id="loadMoreReviewsTabContainer" class="text-center mt-6 hidden">
                                                <button onclick="loadMoreReviewsTab()" id="loadMoreReviewsTabBtn" class="px-6 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors">
                                                    Load More Reviews
                                                </button>
                                            </div>
                                        </div>
                                    @else
                                        <!-- No Reviews State -->
                                        <div class="text-center py-12">
                                            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                                <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                </svg>
                                            </div>
                                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Reviews Yet</h3>
                                            <p class="text-gray-600 mb-4">{{ $user->name }} hasn't received any reviews yet. Be the first to book and share your experience!</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Similar Users Section -->
            @if($similarUsers->count() > 0)
                <div class="mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Similar Users You Might Like</h2>

                    <!-- Container with Light Gradient Background -->
                    <div class="relative rounded-3xl p-8" style="background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 50%, rgba(236, 72, 153, 0.05) 100%);">
                        <!-- Subtle Background Pattern -->
                        <div class="absolute inset-0 opacity-30 rounded-3xl" style="background-image: radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);"></div>

                        <div class="relative z-10">
                            <!-- Users Grid -->
                            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                                @foreach($similarUsers as $similarUser)
                                    <div class="group">
                                        <!-- White Blurred Card -->
                                        <div class="bg-white/70 backdrop-blur-md hover:bg-white/90 hover:backdrop-blur-lg rounded-2xl p-6 border border-white/50 hover:border-blue-300 shadow-lg hover:shadow-2xl hover:shadow-blue-500/20 transition-all duration-300 hover:bg-gradient-to-br hover:from-blue-50/40 hover:to-purple-50/40 transform hover:-translate-y-1">
                                            <!-- Gender Badge (Top Left) and Price (Top Right) -->
                                            <div class="flex justify-between items-center mb-4">
                                                <!-- Gender Badge - Left -->
                                                <span class="inline-block px-2 py-1 text-xs font-medium rounded-full {{ $similarUser->gender === 'male' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800' }}">
                                                    {{ ucfirst($similarUser->gender) }}
                                                </span>
                                                <!-- Price - Right -->
                                                <span class="text-sm font-bold text-gradient-primary">₹{{ number_format($similarUser->hourly_rate, 0) }}/h</span>
                                            </div>

                                            <!-- Profile Image -->
                                            <div class="flex justify-center mb-4">
                                                <div class="w-32 h-32 rounded-full overflow-hidden border-4 border-gray-200 shadow-lg cursor-pointer hover:shadow-xl transition-shadow duration-300" onclick="openImageModal({!! json_encode($similarUser->profile_picture ? asset('storage/' . $similarUser->profile_picture) : ($similarUser->galleryImages->count() > 0 ? $similarUser->galleryImages->first()->image_url : asset('images/default-avatar.png'))) !!}, {!! json_encode($similarUser->name . ' - Profile Picture') !!})">
                                                    @if($similarUser->profile_picture)
                                                        <img src="{{ asset('storage/' . $similarUser->profile_picture) }}"
                                                             alt="{{ $similarUser->name }}"
                                                             class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                                                             loading="lazy"
                                                             onerror="this.src='{{ asset('images/default-avatar.png') }}';">
                                                    @elseif($similarUser->galleryImages->count() > 0)
                                                        <img src="{{ $similarUser->galleryImages->first()->image_url }}"
                                                             alt="{{ $similarUser->name }}"
                                                             class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                                                             loading="lazy"
                                                             onerror="this.src='{{ asset('images/default-avatar.png') }}';">
                                                    @else
                                                        <img src="{{ asset('images/default-avatar.png') }}"
                                                             alt="{{ $similarUser->name }}"
                                                             class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                                                    @endif
                                                </div>
                                            </div>

                                            <!-- Name -->
                                            <div class="text-center mb-4">
                                                <h3 class="font-semibold text-gray-900 text-lg">{{ $similarUser->name }}</h3>
                                            </div>

                                            <!-- Attractive Hobby Display -->
                                            <div class="mb-4">
                                                @if($similarUser->interests)
                                                    <div class="flex flex-wrap justify-center gap-1">
                                                        @foreach(array_slice(explode(',', $similarUser->interests), 0, 2) as $interest)
                                                            <span class="inline-block px-2 py-1 text-xs font-medium rounded-full bg-gradient-to-r from-blue-100 to-purple-100 text-gray-700 border border-gray-200">
                                                                {{ trim($interest) }}
                                                            </span>
                                                        @endforeach
                                                        @if(count(explode(',', $similarUser->interests)) > 2)
                                                            <span class="inline-block px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-600 border border-gray-200">
                                                                +{{ count(explode(',', $similarUser->interests)) - 2 }}
                                                            </span>
                                                        @endif
                                                    </div>
                                                @else
                                                    <div class="text-xs text-gray-500 italic text-center">
                                                        ✨ Exploring new interests
                                                    </div>
                                                @endif
                                            </div>

                                            <!-- View Detail Button -->
                                            <a href="{{ route('find-person.show', $similarUser) }}" class="block w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-2 px-4 rounded-full transition-all duration-200 shadow-md hover:shadow-lg hover:shadow-blue-500/25 transform hover:-translate-y-0.5 text-center">
                                                View Detail
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </section>

    <!-- Success Popup Modal -->
    <div id="successModal" class="fixed inset-0 bg-black bg-opacity-80 z-50 hidden flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 scale-95 opacity-0" id="successModalContent">
            <!-- Success Header -->
            <div class="bg-gradient-to-r from-green-500 to-emerald-600 p-6 rounded-t-2xl text-white text-center">
                <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full mx-auto mb-4 flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <h2 class="text-2xl font-bold mb-2">Successfully Hired!</h2>
                <p class="text-white/90">Your booking has been confirmed</p>
            </div>

            <!-- Success Content -->
            <div class="p-6">
                <div id="successBookingDetails" class="space-y-4">
                    <!-- Booking details will be populated here -->
                </div>

                <!-- Action Buttons -->
                <div class="flex gap-3 mt-6">
                    <button onclick="closeSuccessModal()" class="flex-1 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors">
                        Close
                    </button>
                    <button onclick="goToTransactions()" class="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                        View Transactions
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Image Lightbox Modal -->
    <div id="imageModal" class="fixed inset-0 bg-black z-[9999] hidden" onclick="closeImageModal(event)" style="background-color: rgba(0, 0, 0, 0.9);">
        <div class="relative w-full h-full flex justify-center items-center" onclick="event.stopPropagation()">

            <!-- Image Container with Close Button -->
            <div class="relative max-w-[90vw] max-h-[90vh]">
                <img id="modalImage"
                     src=""
                     alt="Full size image"
                     class="object-contain rounded-lg shadow-2xl transition-all duration-300 border-2 border-white border-opacity-20"
                     style="max-width: 90vw; max-height: 80vh; min-width: 300px; min-height: 200px;">

                <!-- Close Button - Top Right Corner of Image -->
                <button onclick="closeImageModal()" class="absolute -top-4 -right-4 text-white hover:text-gray-200 z-20 bg-black hover:bg-gray-800 rounded-full p-3 transition-all duration-200 shadow-lg">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>

                <!-- Loading Spinner -->
                <div id="imageLoader" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg hidden">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
                </div>
            </div>

            <!-- Previous Button -->
            <button id="prevButton" onclick="previousImage()" class="absolute left-4 md:left-6 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-20 bg-black bg-opacity-60 hover:bg-opacity-80 rounded-full p-2 md:p-3 transition-all duration-200 shadow-lg hidden">
                <svg class="w-5 h-5 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>

            <!-- Next Button -->
            <button id="nextButton" onclick="nextImage()" class="absolute right-4 md:right-6 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-20 bg-black bg-opacity-60 hover:bg-opacity-80 rounded-full p-2 md:p-3 transition-all duration-200 shadow-lg hidden">
                <svg class="w-5 h-5 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>

            <!-- Image Info Bar -->
            <div id="imageInfoBar" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 text-white px-4 py-2 rounded-full text-sm font-medium hidden">
                <span id="imageCounter">1 / 1</span>
                <span id="imageTitle" class="ml-3 hidden md:inline"></span>
            </div>

            <!-- Touch/Swipe Instructions (Mobile) -->
            <div id="swipeInstructions" class="absolute bottom-16 left-1/2 transform -translate-x-1/2 text-white text-xs opacity-70 md:hidden">
                Swipe left/right to navigate • Tap to close
            </div>

        </div>
    </div>



    <!-- Modern Payment Modal -->
    <div id="paymentModal" class="fixed inset-0 z-50 hidden" onclick="closePaymentModal(event)">
        <div class="absolute inset-0 bg-black/50 backdrop-blur-sm"></div>

        <div class="relative w-full h-full flex items-center justify-center p-4">
            <div class="bg-white rounded-3xl shadow-2xl max-w-sm w-full transform transition-all duration-300 scale-95 opacity-0"
                 id="paymentModalContent" onclick="event.stopPropagation()"
                 style="box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);">

                <!-- Close Button -->
                <button onclick="closePaymentModal()"
                        class="absolute -top-4 -right-4 w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center text-gray-400 hover:text-gray-600 hover:shadow-xl transition-all duration-200 z-10">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>

                <!-- Header -->
                <div class="p-8 pb-6 text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl mx-auto mb-4 flex items-center justify-center shadow-lg">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Secure Payment</h3>
                    <p class="text-gray-600">Complete your booking payment</p>
                </div>

                <!-- Payment Content -->
                <div class="px-8 pb-8 space-y-6">
                    <!-- Booking Summary -->
                    <div id="bookingSummary" class="hidden">
                        <!-- Content will be populated by JavaScript -->
                    </div>

                    <!-- Amount Display -->
                    <div class="bg-gradient-to-br from-green-500 via-blue-600 to-purple-600 rounded-2xl p-6 text-white text-center">
                        <div class="flex items-center justify-center mb-3">
                            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                            <span class="text-lg font-semibold">Amount to Pay</span>
                        </div>
                        <div id="paymentAmount" class="text-4xl font-bold mb-3">₹0</div>
                        <div class="text-sm text-white/80">Secure • Encrypted • Instant</div>
                    </div>

                    <!-- Payment Timer -->
                    <div id="paymentTimer" class="text-center text-sm text-orange-600 font-medium bg-orange-50 rounded-lg p-3 hidden">
                        Payment expires in: 30:00
                    </div>

                    <!-- Payment Breakdown -->
                    <div id="paymentBreakdown" class="hidden mb-6"></div>

                    <!-- Trust Indicators -->
                    <div class="flex items-center justify-center space-x-2 text-gray-500 text-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        <span class="font-medium">Powered by Razorpay</span>
                    </div>

                    <!-- Payment Button -->
                    <button id="razorpayButton"
                            class="w-full px-6 py-4 bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white rounded-2xl transition-all duration-200 font-bold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
                        <span class="flex items-center justify-center space-x-3">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            <span class="text-lg">Complete Payment</span>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </span>
                    </button>

                    <!-- Cancel Booking Button -->
                    <button id="cancelBookingButton" onclick="handleBookingCancellation()"
                            class="w-full px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl transition-all duration-200 font-medium border border-gray-300 hover:border-gray-400">
                        <span class="flex items-center justify-center space-x-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            <span>Cancel Booking</span>
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Helper functions for user-friendly messages
        function showSuccessMessage(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #10b981;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 100000;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                font-weight: 500;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }

        function showErrorMessage(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #ef4444;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 100000;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                font-weight: 500;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 4000);
        }

        // Gallery navigation variables
        let currentImageIndex = 0;
        let galleryImages = [];
        let isGalleryMode = false;

        // Initialize gallery images
        function initializeGallery() {
            galleryImages = [];
            @if($user->galleryImages->count() > 0)
                @foreach($user->galleryImages as $index => $image)
                    galleryImages.push({
                        url: {!! json_encode($image->image_url) !!},
                        title: {!! json_encode($user->name . ' - Gallery Image ' . ($index + 1)) !!}
                    });
                @endforeach
            @endif
        }

        // Open gallery image at specific index
        function openGalleryImage(index) {
            if (galleryImages.length === 0) {
                console.log('No gallery images available');
                return;
            }

            if (index >= 0 && index < galleryImages.length) {
                const imageData = galleryImages[index];
                openImageModal(imageData.url, imageData.title, index);
            } else {
                console.log('Invalid gallery image index:', index);
            }
        }

        // Open profile image (if gallery exists, start gallery mode from profile image)
        function openProfileImage() {
            @if($user->profile_picture)
                const profileImageUrl = {!! json_encode(asset('storage/' . $user->profile_picture)) !!};
                const profileTitle = {!! json_encode($user->name . ' - Profile Picture') !!};

                // If gallery exists, add profile image as first item and start gallery mode
                if (galleryImages.length > 0) {
                    // Create a temporary gallery array with profile image first
                    const tempGallery = [{
                        url: profileImageUrl,
                        title: profileTitle
                    }, ...galleryImages];

                    // Temporarily replace gallery for this session
                    const originalGallery = [...galleryImages];
                    galleryImages = tempGallery;

                    // Open in gallery mode starting from profile image (index 0)
                    openImageModal(profileImageUrl, profileTitle, 0);

                    // Restore original gallery after modal closes
                    setTimeout(() => {
                        galleryImages = originalGallery;
                    }, 100);
                } else {
                    // No gallery, just open profile image
                    openImageModal(profileImageUrl, profileTitle);
                }
            @elseif($user->galleryImages->count() > 0)
                // No profile picture, but has gallery - open first gallery image
                openGalleryImage(0);
            @else
                // No images at all
                console.log('No images available');
            @endif
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeGallery();
        });

        function openImageModal(imageUrl, imageTitle = 'Gallery Image', imageIndex = null) {
            console.log('Opening modal with image:', imageUrl);

            if (imageUrl && imageUrl !== '') {
                const modal = document.getElementById('imageModal');
                const modalImage = document.getElementById('modalImage');
                const imageLoader = document.getElementById('imageLoader');
                const prevButton = document.getElementById('prevButton');
                const nextButton = document.getElementById('nextButton');
                const imageInfoBar = document.getElementById('imageInfoBar');
                const imageCounter = document.getElementById('imageCounter');
                const imageTitleElement = document.getElementById('imageTitle');
                const swipeInstructions = document.getElementById('swipeInstructions');

                // Check if this is a gallery image
                isGalleryMode = imageIndex !== null;
                if (isGalleryMode && galleryImages.length > 1) {
                    currentImageIndex = imageIndex;
                    prevButton.classList.remove('hidden');
                    nextButton.classList.remove('hidden');
                    imageInfoBar.classList.remove('hidden');
                    swipeInstructions.classList.remove('hidden');

                    // Update counter and title
                    imageCounter.textContent = `${currentImageIndex + 1} / ${galleryImages.length}`;
                    imageTitleElement.textContent = imageTitle;
                } else {
                    prevButton.classList.add('hidden');
                    nextButton.classList.add('hidden');
                    imageInfoBar.classList.add('hidden');
                    swipeInstructions.classList.add('hidden');
                }

                // Show modal and loader
                modal.style.display = 'block';
                modal.classList.remove('hidden');
                imageLoader.classList.remove('hidden');
                document.body.style.overflow = 'hidden';

                // Load image with better animation
                modalImage.onload = function() {
                    imageLoader.classList.add('hidden');
                    modalImage.style.opacity = '0';
                    modalImage.style.transform = 'scale(0.9)';

                    // Smooth fade in animation
                    setTimeout(() => {
                        modalImage.style.transition = 'opacity 0.4s ease, transform 0.4s ease';
                        modalImage.style.opacity = '1';
                        modalImage.style.transform = 'scale(1)';
                    }, 50);
                };

                modalImage.onerror = function() {
                    imageLoader.classList.add('hidden');
                    // Show fallback
                    modalImage.src = '{{ asset('images/default-avatar.png') }}';
                };

                modalImage.src = imageUrl;
                console.log('Modal opened successfully');
            } else {
                console.log('No image URL provided');
            }
        }

        function previousImage() {
            if (!isGalleryMode || galleryImages.length <= 1) return;

            currentImageIndex = currentImageIndex > 0 ? currentImageIndex - 1 : galleryImages.length - 1;
            const currentImage = galleryImages[currentImageIndex];

            updateImageInModal(currentImage);
        }

        function nextImage() {
            if (!isGalleryMode || galleryImages.length <= 1) return;

            currentImageIndex = currentImageIndex < galleryImages.length - 1 ? currentImageIndex + 1 : 0;
            const currentImage = galleryImages[currentImageIndex];

            updateImageInModal(currentImage);
        }

        function updateImageInModal(imageData) {
            const modalImage = document.getElementById('modalImage');
            const imageLoader = document.getElementById('imageLoader');
            const imageCounter = document.getElementById('imageCounter');
            const imageTitleElement = document.getElementById('imageTitle');

            // Show loader and fade out current image
            imageLoader.classList.remove('hidden');
            modalImage.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
            modalImage.style.opacity = '0.3';
            modalImage.style.transform = 'scale(0.95)';

            // Update counter and title
            imageCounter.textContent = `${currentImageIndex + 1} / ${galleryImages.length}`;
            imageTitleElement.textContent = imageData.title;

            modalImage.onload = function() {
                imageLoader.classList.add('hidden');

                // Smooth fade in animation
                setTimeout(() => {
                    modalImage.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    modalImage.style.opacity = '1';
                    modalImage.style.transform = 'scale(1)';
                }, 50);
            };

            modalImage.onerror = function() {
                imageLoader.classList.add('hidden');
                // Show fallback
                modalImage.src = '{{ asset('images/default-avatar.png') }}';
            };

            modalImage.src = imageData.url;
        }

        function closeImageModal(event) {
            if (event) {
                event.stopPropagation();
            }

            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const imageInfoBar = document.getElementById('imageInfoBar');
            const swipeInstructions = document.getElementById('swipeInstructions');

            // Fade out animation
            modalImage.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            modalImage.style.opacity = '0';
            modalImage.style.transform = 'scale(0.8)';

            // Hide info elements
            imageInfoBar.classList.add('hidden');
            swipeInstructions.classList.add('hidden');

            setTimeout(() => {
                modal.classList.add('hidden');
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';

                // Reset image and gallery mode
                modalImage.src = '';
                modalImage.style.opacity = '1';
                modalImage.style.transform = 'scale(1)';
                modalImage.style.transition = '';
                isGalleryMode = false;
                currentImageIndex = 0;
            }, 300);

            console.log('Modal closed');
        }

        // Enhanced keyboard navigation
        document.addEventListener('keydown', function(event) {
            const modal = document.getElementById('imageModal');
            if (!modal.classList.contains('hidden')) {
                switch(event.key) {
                    case 'Escape':
                        event.preventDefault();
                        closeImageModal();
                        break;
                    case 'ArrowLeft':
                    case 'a':
                    case 'A':
                        event.preventDefault();
                        previousImage();
                        break;
                    case 'ArrowRight':
                    case 'd':
                    case 'D':
                        event.preventDefault();
                        nextImage();
                        break;
                    case ' ': // Spacebar
                        event.preventDefault();
                        nextImage();
                        break;
                }
            }
        });

        // Touch/Swipe support for mobile
        let touchStartX = 0;
        let touchEndX = 0;
        let touchStartY = 0;
        let touchEndY = 0;

        document.getElementById('imageModal').addEventListener('touchstart', function(event) {
            touchStartX = event.changedTouches[0].screenX;
            touchStartY = event.changedTouches[0].screenY;
        });

        document.getElementById('imageModal').addEventListener('touchend', function(event) {
            touchEndX = event.changedTouches[0].screenX;
            touchEndY = event.changedTouches[0].screenY;
            handleSwipe();
        });

        function handleSwipe() {
            const modal = document.getElementById('imageModal');
            if (modal.classList.contains('hidden')) return;

            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;
            const minSwipeDistance = 50;

            // Check if it's a horizontal swipe (not vertical scroll)
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
                if (deltaX > 0) {
                    // Swipe right - previous image
                    previousImage();
                } else {
                    // Swipe left - next image
                    nextImage();
                }
            }
            // If it's a small tap (not a swipe), close modal
            else if (Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10) {
                closeImageModal();
            }
        }

        // Booking system variables
        let currentBooking = null;
        let hourlyRate = {{ floatval($user->hourly_rate ?? 0) }};
        let platformFee = {{ floatval(\App\Models\Setting::get('platform_fee', 0)) }};

        // Geolocation functions
        function showLocationStatus(message, isError = false) {
            const statusDiv = document.getElementById('locationStatus');
            statusDiv.textContent = message;
            statusDiv.className = `text-xs ${isError ? 'text-red-500' : 'text-green-500'}`;
            statusDiv.classList.remove('hidden');

            // Hide status after 5 seconds if not an error
            if (!isError) {
                setTimeout(() => {
                    statusDiv.classList.add('hidden');
                }, 5000);
            }
        }

        async function reverseGeocode(latitude, longitude) {
            try {
                // Using OpenStreetMap Nominatim API for reverse geocoding with higher zoom for precision
                const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=18&addressdetails=1&extratags=1`);
                const data = await response.json();

                if (data && data.display_name) {
                    const address = data.address || {};
                    let formattedLocation = '';

                    // Prioritize local landmarks and specific locations
                    const localNames = [
                        address.amenity,
                        address.shop,
                        address.tourism,
                        address.leisure,
                        address.building,
                        address.office
                    ].filter(Boolean);

                    // Check for local area names, chowks, circles, etc.
                    const areaNames = [
                        address.neighbourhood,
                        address.suburb,
                        address.quarter,
                        address.city_district
                    ].filter(Boolean);

                    // Build precise address starting with most specific
                    if (localNames.length > 0) {
                        formattedLocation += localNames[0] + ', ';
                    }

                    // Add road/street information
                    if (address.road) {
                        formattedLocation += address.road + ', ';
                    }

                    // Add area/neighborhood (chowk, area names)
                    if (areaNames.length > 0) {
                        formattedLocation += areaNames[0] + ', ';
                    }

                    // Add city/town
                    if (address.city || address.town || address.village) {
                        formattedLocation += (address.city || address.town || address.village);
                    }

                    // Clean up formatting
                    formattedLocation = formattedLocation.replace(/,\s*$/, '').replace(/,\s*,/g, ',').trim();

                    // If we got a good local address, return it
                    if (formattedLocation && formattedLocation.length > 10) {
                        return formattedLocation;
                    }

                    // Fallback to display_name but try to extract meaningful parts
                    const displayParts = data.display_name.split(',').map(part => part.trim());
                    if (displayParts.length >= 3) {
                        return displayParts.slice(0, 3).join(', ');
                    }

                    return data.display_name;
                }
                throw new Error('No address found');
            } catch (error) {
                return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
            }
        }

        // Autocomplete functionality
        let searchTimeout;
        let currentSuggestions = [];

        async function searchLocations(query) {
            if (query.length < 3) return [];

            try {
                // Using Nominatim search API for autocomplete
                const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=8&countrycodes=in&addressdetails=1&extratags=1`);
                const data = await response.json();

                return data.map(item => {
                    const address = item.address || {};
                    let displayName = '';

                    // Format for Indian locations with local names
                    const localNames = [
                        address.amenity,
                        address.shop,
                        address.tourism,
                        address.leisure
                    ].filter(Boolean);

                    if (localNames.length > 0) {
                        displayName += localNames[0] + ', ';
                    }

                    if (address.road) {
                        displayName += address.road + ', ';
                    }

                    if (address.neighbourhood || address.suburb) {
                        displayName += (address.neighbourhood || address.suburb) + ', ';
                    }

                    if (address.city || address.town) {
                        displayName += (address.city || address.town);
                    }

                    displayName = displayName.replace(/,\s*$/, '').replace(/,\s*,/g, ',').trim();

                    return {
                        display_name: displayName || item.display_name,
                        full_address: item.display_name,
                        lat: item.lat,
                        lon: item.lon
                    };
                });
            } catch (error) {
                return [];
            }
        }

        function showLocationSuggestions(suggestions) {
            const dropdown = document.getElementById('locationDropdown');
            const suggestionsContainer = document.getElementById('locationSuggestions');
            const loadingDiv = document.getElementById('locationLoading');
            const noResultsDiv = document.getElementById('locationNoResults');

            // Hide loading and no results
            loadingDiv.classList.add('hidden');
            noResultsDiv.classList.add('hidden');

            if (suggestions.length === 0) {
                noResultsDiv.classList.remove('hidden');
                dropdown.classList.remove('hidden');
                return;
            }

            // Clear previous suggestions
            suggestionsContainer.innerHTML = '';

            suggestions.forEach((suggestion, index) => {
                const suggestionDiv = document.createElement('div');
                suggestionDiv.className = 'px-4 py-3 hover:bg-red-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-150';
                suggestionDiv.innerHTML = `
                    <div class="flex items-start space-x-3">
                        <svg class="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5S10.62 6.5 12 6.5s2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"></path>
                        </svg>
                        <div class="flex-1 min-w-0">
                            <div class="text-sm font-medium text-gray-900 truncate">${suggestion.display_name}</div>
                            <div class="text-xs text-gray-500 truncate">${suggestion.full_address}</div>
                        </div>
                    </div>
                `;

                suggestionDiv.addEventListener('click', () => {
                    document.getElementById('location').value = suggestion.display_name;
                    hideLocationDropdown();
                });

                suggestionsContainer.appendChild(suggestionDiv);
            });

            dropdown.classList.remove('hidden');
            currentSuggestions = suggestions;
        }

        function hideLocationDropdown() {
            document.getElementById('locationDropdown').classList.add('hidden');
            currentSuggestions = [];
        }

        function showLocationLoading() {
            const dropdown = document.getElementById('locationDropdown');
            const loadingDiv = document.getElementById('locationLoading');
            const noResultsDiv = document.getElementById('locationNoResults');

            document.getElementById('locationSuggestions').innerHTML = '';
            noResultsDiv.classList.add('hidden');
            loadingDiv.classList.remove('hidden');
            dropdown.classList.remove('hidden');
        }

        function getCurrentLocation() {
            const locationInput = document.getElementById('location');
            const useLocationBtn = document.getElementById('useCurrentLocationBtn');

            // Check if geolocation is supported
            if (!navigator.geolocation) {
                showLocationStatus('Geolocation is not supported by this browser', true);
                return;
            }

            // Show loading state
            useLocationBtn.disabled = true;
            useLocationBtn.innerHTML = `
                <svg class="w-4 h-4 inline mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                <span class="hidden sm:inline">Getting...</span>
            `;
            showLocationStatus('Getting your location...');

            // Get current position
            navigator.geolocation.getCurrentPosition(
                async function(position) {
                    const latitude = position.coords.latitude;
                    const longitude = position.coords.longitude;

                    try {
                        const address = await reverseGeocode(latitude, longitude);
                        locationInput.value = address;
                        showLocationStatus('Location detected successfully!');
                    } catch (error) {
                        locationInput.value = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
                        showLocationStatus('Location detected (coordinates only)');
                    }

                    // Reset button
                    useLocationBtn.disabled = false;
                    useLocationBtn.innerHTML = `
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span class="hidden sm:inline">Use Current</span>
                    `;
                },
                function(error) {
                    let errorMessage = '';
                    switch(error.code) {
                        case error.PERMISSION_DENIED:
                            errorMessage = 'Location access denied. Please enter location manually.';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMessage = 'Location information unavailable. Please enter location manually.';
                            break;
                        case error.TIMEOUT:
                            errorMessage = 'Location request timed out. Please try again or enter manually.';
                            break;
                        default:
                            errorMessage = 'An unknown error occurred. Please enter location manually.';
                            break;
                    }

                    showLocationStatus(errorMessage, true);

                    // Try IP-based location as fallback
                    tryIPLocation();

                    // Reset button
                    useLocationBtn.disabled = false;
                    useLocationBtn.innerHTML = `
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span class="hidden sm:inline">Use Current</span>
                    `;
                },
                {
                    enableHighAccuracy: false,
                    timeout: 15000,
                    maximumAge: 600000 // 10 minutes
                }
            );
        }

        // IP-based location fallback
        async function tryIPLocation() {
            try {
                showLocationStatus('Trying IP-based location detection...');

                // Using ipapi.co for IP-based location (free tier)
                const response = await fetch('https://ipapi.co/json/');
                const data = await response.json();

                if (data && data.city && data.region) {
                    const locationInput = document.getElementById('location');
                    const ipLocation = `${data.city}, ${data.region}`;
                    locationInput.value = ipLocation;
                    showLocationStatus(`Approximate location detected: ${ipLocation} (based on IP address)`);
                } else {
                    showLocationStatus('Could not detect location. Please enter manually.', true);
                }
            } catch (error) {
                showLocationStatus('Could not detect location. Please enter manually.', true);
            }
        }

        // Scroll to booking form function
        function scrollToBookingForm() {
            const bookingSection = document.getElementById('bookingFormSection');
            if (bookingSection) {
                bookingSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // Optional: Add a subtle highlight effect
                bookingSection.style.transition = 'box-shadow 0.3s ease';
                bookingSection.style.boxShadow = '0 0 20px rgba(236, 72, 153, 0.3)';
                setTimeout(() => {
                    bookingSection.style.boxShadow = '';
                }, 2000);
            }
        }

        function openPaymentModal() {
            const modal = document.getElementById('paymentModal');
            const content = document.getElementById('paymentModalContent');

            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // Animate in
            setTimeout(() => {
                content.style.transform = 'scale(1)';
                content.style.opacity = '1';
            }, 10);
        }

        function closePaymentModal(event) {
            if (event) {
                event.stopPropagation();
            }

            const modal = document.getElementById('paymentModal');
            const content = document.getElementById('paymentModalContent');

            // Animate out
            content.style.transform = 'scale(0.95)';
            content.style.opacity = '0';

            setTimeout(() => {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }, 300);
        }

        // Calculate duration between start and end time (for booking form)
        function calculateDurationForBooking(startTime, endTime) {
            try {
                if (!startTime || !endTime) return 0;

                // Parse time strings properly
                const startParts = startTime.split(':');
                const endParts = endTime.split(':');

                if (startParts.length !== 2 || endParts.length !== 2) {
                    return 0;
                }

                const startHour = parseInt(startParts[0], 10);
                const startMin = parseInt(startParts[1], 10);
                const endHour = parseInt(endParts[0], 10);
                const endMin = parseInt(endParts[1], 10);

                // Check for valid numbers
                if (isNaN(startHour) || isNaN(startMin) || isNaN(endHour) || isNaN(endMin)) {
                    return 0;
                }

                // Calculate total minutes
                const startTotalMin = startHour * 60 + startMin;
                const endTotalMin = endHour * 60 + endMin;
                const diffMin = endTotalMin - startTotalMin;



                // ALLOW NEGATIVE DURATION FOR NOW - Let backend handle validation
                // if (diffMin <= 0) return 0;

                const diffHours = diffMin / 60;

                // Return actual duration (no minimum restriction for selection)
                return diffHours;
            } catch (error) {
                return 0;
            }
        }

        // Calculate billing duration (minimum 0.5 hours for charging)
        function calculateBillingDuration(actualDuration) {
            // Minimum billing is 0.5 hours, but round up to nearest 30-minute interval
            const minBilling = 0.5;
            if (actualDuration <= 0) return 0;

            if (actualDuration < minBilling) {
                return minBilling;
            }

            // Round up to nearest 30-minute interval
            const thirtyMinuteBlocks = Math.ceil(actualDuration * 2);
            return thirtyMinuteBlocks / 2;
        }

        // Update payment breakdown display
        function updatePaymentBreakdown(totalAmount, billingDuration, actualDuration) {
            // Get current user's wallet balance from the wallet relationship
            const walletBalance = {{ auth()->user()->getWallet()->balance ?? 0 }};
            const walletUsage = Math.min(walletBalance, totalAmount);
            const needToPayOnline = Math.max(0, totalAmount - walletUsage);
            const creditToWallet = 0; // No credit for new bookings

            // Determine billing note
            let billingNote = '';
            if (billingDuration > actualDuration) {
                billingNote = ' (minimum 0.5 hours cost added)';
            }

            // Update breakdown display with calculation format
            const hourlyRate = {{ floatval($user->hourly_rate ?? 0) }};
            const baseAmount = hourlyRate * billingDuration;
            const platformFeeAmount = {{ floatval(\App\Models\Setting::get('platform_fee', 0)) }};

            // Show base amount in Amount row
            const breakdownText = `₹${hourlyRate}/h × ${billingDuration} = ₹${Math.round(baseAmount)}`;
            document.getElementById('breakdownAmount').textContent = breakdownText;

            // Calculate final amounts correctly
            const finalNeedToPayOnline = Math.max(0, (baseAmount + platformFeeAmount) - walletUsage);
            const finalTotal = baseAmount + platformFeeAmount;

            // Handle platform fee display (2nd in order)
            const platformFeeRow = document.getElementById('platformFeeRow');
            const platformFeeElement = document.getElementById('breakdownPlatformFee');
            if (platformFeeAmount > 0) {
                platformFeeRow.classList.remove('hidden');
                platformFeeElement.textContent = '₹' + platformFeeAmount;
            } else {
                platformFeeRow.classList.add('hidden');
            }

            // Handle wallet balance display (3rd in order)
            const walletBalanceRow = document.getElementById('walletBalanceRow');
            const walletElement = document.getElementById('breakdownWallet');
            if (walletUsage > 0) {
                walletBalanceRow.classList.remove('hidden');
                walletElement.textContent = '-₹' + Math.round(walletUsage);
                walletElement.className = 'py-2 text-right text-green-600';
            } else {
                walletBalanceRow.classList.add('hidden');
            }

            // Handle need to pay online display (4th in order)
            const needToPayOnlineRow = document.getElementById('needToPayOnlineRow');
            const onlineElement = document.getElementById('breakdownOnline');
            if (finalNeedToPayOnline > 0) {
                needToPayOnlineRow.classList.remove('hidden');
                onlineElement.textContent = '₹' + Math.round(finalNeedToPayOnline);
                onlineElement.className = 'py-2 text-right text-red-600';
            } else {
                needToPayOnlineRow.classList.add('hidden');
            }

            // Handle credit to wallet display (5th in order)
            const creditToWalletRow = document.getElementById('creditToWalletRow');
            const creditElement = document.getElementById('breakdownCredit');
            if (creditToWallet > 0) {
                creditToWalletRow.classList.remove('hidden');
                creditElement.textContent = '₹' + Math.round(creditToWallet);
            } else {
                creditToWalletRow.classList.add('hidden');
            }

            // Final total (always shown)
            document.getElementById('breakdownFinal').textContent = '₹' + Math.round(finalTotal);

            // Update colors for final total
            const finalEl = document.getElementById('breakdownFinal');
            finalEl.className = 'py-2 text-right text-lg ' + (finalNeedToPayOnline > 0 ? 'text-red-600' : 'text-green-600');

            // Show breakdown
            document.getElementById('paymentBreakdownDisplay').classList.remove('hidden');
        }

        // Optimized total amount calculation with performance improvements
        let updateTotalAmountPending = false;
        function updateTotalAmount() {
            // Debounce rapid calls to prevent excessive DOM manipulation
            if (updateTotalAmountPending) return;
            updateTotalAmountPending = true;

            requestAnimationFrame(() => {
                try {
            const startTime = window.startTimeDropdown ? window.startTimeDropdown.getValue() : document.getElementById('start_time_value')?.value || '';
            const endTime = window.endTimeDropdown ? window.endTimeDropdown.getValue() : document.getElementById('end_time_value')?.value || '';
            const actualDuration = calculateDurationForBooking(startTime, endTime);
            const billingDuration = calculateBillingDuration(actualDuration);

            const baseAmount = hourlyRate * billingDuration;
            const total = baseAmount + platformFee;

            // Format duration display properly
            let durationText = '';
            if (!endTime) {
                durationText = 'Select end time';
            } else if (actualDuration > 0) {
                const hours = Math.floor(actualDuration);
                const minutes = Math.round((actualDuration - hours) * 60);

                if (hours === 0) {
                    durationText = `${minutes} minutes`;
                } else if (minutes === 0) {
                    durationText = hours === 1 ? '1 hour' : `${hours} hours`;
                } else {
                    durationText = hours === 1 ? `1 hour ${minutes} min` : `${hours} hours ${minutes} min`;
                }
            } else {
                durationText = '0 hours';
            }

                    // Batch DOM updates to prevent layout thrashing
                    const durationElement = document.getElementById('calculatedDuration');
                    if (durationElement && durationElement.textContent !== durationText) {
                        durationElement.textContent = durationText;
                    }

            // Show billing information if different from actual duration
            const billingInfo = document.getElementById('billingInfo');
            if (actualDuration > 0 && actualDuration < 0.5) {
                billingInfo.innerHTML = `<div class="text-xs text-orange-600 mt-1">
                    <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Minimum billing: 0.5 hours (₹${Math.round(hourlyRate * 0.5).toLocaleString()})
                </div>`;
                billingInfo.classList.remove('hidden');
            } else if (actualDuration !== billingDuration) {
                const billingHours = Math.floor(billingDuration);
                const billingMinutes = Math.round((billingDuration - billingHours) * 60);
                let billingText = '';

                if (billingHours === 0) {
                    billingText = `${billingMinutes} minutes`;
                } else if (billingMinutes === 0) {
                    billingText = billingHours === 1 ? '1 hour' : `${billingHours} hours`;
                } else {
                    billingText = billingHours === 1 ? `1 hour ${billingMinutes} min` : `${billingHours} hours ${billingMinutes} min`;
                }

                billingInfo.innerHTML = `<div class="text-xs text-blue-600 mt-1">
                    <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Billing duration: ${billingText} (rounded to 30-min intervals)
                </div>`;
                billingInfo.classList.remove('hidden');
            } else {
                billingInfo.classList.add('hidden');
            }

                    // Show/hide duration display and update payment breakdown
                    const durationDisplay = document.getElementById('durationDisplay');
                    const paymentBreakdownDisplay = document.getElementById('paymentBreakdownDisplay');

                    if (actualDuration > 0) {
                        if (durationDisplay && durationDisplay.classList.contains('hidden')) {
                            durationDisplay.classList.remove('hidden');
                        }
                        updatePaymentBreakdown(total, billingDuration, actualDuration);
                    } else {
                        if (durationDisplay && !durationDisplay.classList.contains('hidden')) {
                            durationDisplay.classList.add('hidden');
                        }
                        if (paymentBreakdownDisplay && !paymentBreakdownDisplay.classList.contains('hidden')) {
                            paymentBreakdownDisplay.classList.add('hidden');
                        }
                    }
                } finally {
                    updateTotalAmountPending = false;
                }
            });
        }

        // Validate time range
        function validateTimeRange(startTime, endTime) {
            if (!startTime || !endTime) return true; // Allow empty values

            const start = new Date(`2000-01-01 ${startTime}:00`);
            const end = new Date(`2000-01-01 ${endTime}:00`);

            return end > start;
        }

        // DISABLED - Set minimum end time based on start time
        function updateEndTimeMin(startTime) {
            // DISABLED ALL VALIDATION - Let user select any time
            console.log('updateEndTimeMin called but disabled');
            return;

            // const endTimeInput = document.getElementById('end_time');
            // if (startTime) {
            //     // Set minimum end time to 15 minutes after start time (allow any duration)
            //     const [hours, minutes] = startTime.split(':');
            //     const startDate = new Date(`2000-01-01 ${startTime}:00`);
            //     startDate.setMinutes(startDate.getMinutes() + 15);
            //
            //     const minEndTime = startDate.toTimeString().slice(0, 5);
            //     endTimeInput.min = minEndTime;
            //
            //     // Clear end time if it's now invalid
            //     if (endTimeInput.value && !validateTimeRange(startTime, endTimeInput.value)) {
            //         endTimeInput.value = '';
            //         updateTotalAmount();
            //     }
            // } else {
            //     endTimeInput.min = '09:00';
            // }
        }

        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {
            // Check for update booking parameters and pre-fill form
            checkAndPreFillUpdateBooking();

            // Add change listeners for update booking mode
            setTimeout(() => {
                addChangeListeners();
            }, 2000);

            // Initialize gallery
            initializeGallery();

            // Prevent modal from closing when clicking on the image or image container
            const modalImage = document.getElementById('modalImage');
            const imageContainer = modalImage?.parentElement;

            if (modalImage) {
                modalImage.addEventListener('click', function(event) {
                    event.stopPropagation();
                });
            }

            if (imageContainer) {
                imageContainer.addEventListener('click', function(event) {
                    event.stopPropagation();
                });
            }

            // Initialize booking system
            const hireMeButton = document.getElementById('hireMeButton');
            const bookingDateInput = document.getElementById('booking_date');
            const startTimeInput = document.getElementById('start_time');
            const endTimeInput = document.getElementById('end_time');
            const bookingForm = document.getElementById('bookingForm');
            const useCurrentLocationBtn = document.getElementById('useCurrentLocationBtn');

            // Hire Me button click - scroll to booking form
            if (hireMeButton) {
                hireMeButton.addEventListener('click', scrollToBookingForm);
            }

            // Use Current Location button click
            if (useCurrentLocationBtn) {
                useCurrentLocationBtn.addEventListener('click', getCurrentLocation);
            }

            // Location autocomplete functionality
            const locationInput = document.getElementById('location');
            if (locationInput) {
                // Input event for autocomplete
                locationInput.addEventListener('input', function(e) {
                    const query = e.target.value.trim();

                    // Clear timeout if user is still typing
                    clearTimeout(searchTimeout);

                    if (query.length < 3) {
                        hideLocationDropdown();
                        return;
                    }

                    // Show loading state
                    showLocationLoading();

                    // Debounce search to avoid excessive API calls
                    searchTimeout = setTimeout(async () => {
                        try {
                            const suggestions = await searchLocations(query);
                            showLocationSuggestions(suggestions);
                        } catch (error) {
                            console.error('Search error:', error);
                            hideLocationDropdown();
                        }
                    }, 300); // 300ms debounce
                });

                // Hide dropdown when clicking outside
                locationInput.addEventListener('blur', function(e) {
                    // Delay hiding to allow click on suggestions
                    setTimeout(() => {
                        hideLocationDropdown();
                    }, 150);
                });

                // Show dropdown when focusing if there are suggestions
                locationInput.addEventListener('focus', function(e) {
                    if (currentSuggestions.length > 0) {
                        document.getElementById('locationDropdown').classList.remove('hidden');
                    }
                });

                // Handle keyboard navigation
                locationInput.addEventListener('keydown', function(e) {
                    const dropdown = document.getElementById('locationDropdown');
                    const suggestions = document.querySelectorAll('#locationSuggestions > div');

                    if (dropdown.classList.contains('hidden') || suggestions.length === 0) {
                        return;
                    }

                    let currentIndex = Array.from(suggestions).findIndex(s => s.classList.contains('bg-red-100'));

                    if (e.key === 'ArrowDown') {
                        e.preventDefault();
                        if (currentIndex < suggestions.length - 1) {
                            if (currentIndex >= 0) suggestions[currentIndex].classList.remove('bg-red-100');
                            suggestions[currentIndex + 1].classList.add('bg-red-100');
                        }
                    } else if (e.key === 'ArrowUp') {
                        e.preventDefault();
                        if (currentIndex > 0) {
                            suggestions[currentIndex].classList.remove('bg-red-100');
                            suggestions[currentIndex - 1].classList.add('bg-red-100');
                        }
                    } else if (e.key === 'Enter') {
                        e.preventDefault();
                        if (currentIndex >= 0) {
                            suggestions[currentIndex].click();
                        }
                    } else if (e.key === 'Escape') {
                        hideLocationDropdown();
                    }
                });
            }

            // Start time change event
            if (startTimeInput) {
                startTimeInput.addEventListener('change', function() {
                    updateEndTimeOptions(this.value);
                    updateTotalAmount();
                });
            }

            // End time change event - DISABLED VALIDATION
            if (endTimeInput) {
                endTimeInput.addEventListener('change', function() {
                    // REMOVED TIME VALIDATION - Let backend handle it
                    // const startTime = startTimeInput.value;
                    // const endTime = this.value;
                    // if (startTime && endTime && !validateTimeRange(startTime, endTime)) {
                    //     alert('End time must be after start time');
                    //     this.value = '';
                    // }
                    updateTotalAmount();
                });
            }

            // Booking form submission
            bookingForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                // SIMPLE APPROACH - Just ensure hidden inputs are set and skip complex validation
                if (window.startTimeDropdown && window.startTimeDropdown.getValue()) {
                    const startTimeHidden = document.getElementById('start_time_value');
                    if (startTimeHidden) startTimeHidden.value = window.startTimeDropdown.getValue();
                }
                if (window.endTimeDropdown && window.endTimeDropdown.getValue()) {
                    const endTimeHidden = document.getElementById('end_time_value');
                    if (endTimeHidden) endTimeHidden.value = window.endTimeDropdown.getValue();
                }

                // Basic validation - only check if times are selected
                const startTimeValue = window.startTimeDropdown ? window.startTimeDropdown.getValue() : '';
                const endTimeValue = window.endTimeDropdown ? window.endTimeDropdown.getValue() : '';

                if (!startTimeValue) {
                    showErrorMessage('Please select a start time');
                    showBookingLoadingState(false);
                    return;
                }

                if (!endTimeValue) {
                    showErrorMessage('Please select an end time');
                    showBookingLoadingState(false);
                    return;
                }

                // SKIP TIME COMPARISON VALIDATION - Let backend handle it

                // Show loading state
                showBookingLoadingState(true);

                const formData = new FormData(this);
                const location = formData.get('location');
                const bookingDate = formData.get('booking_date');

                // Get time values - use multiple fallback methods
                let startTime = '';
                let endTime = '';

                // Method 1: From dropdowns
                if (window.startTimeDropdown && window.startTimeDropdown.getValue()) {
                    startTime = window.startTimeDropdown.getValue();
                }
                if (window.endTimeDropdown && window.endTimeDropdown.getValue()) {
                    endTime = window.endTimeDropdown.getValue();
                }

                // Method 2: From hidden inputs
                if (!startTime) {
                    const startHidden = document.getElementById('start_time_value');
                    startTime = startHidden ? startHidden.value : '';
                }
                if (!endTime) {
                    const endHidden = document.getElementById('end_time_value');
                    endTime = endHidden ? endHidden.value : '';
                }

                // Method 3: From data attributes
                if (!startTime) {
                    const startInput = document.getElementById('start_time');
                    startTime = startInput ? startInput.getAttribute('data-time-value') : '';
                }
                if (!endTime) {
                    const endInput = document.getElementById('end_time');
                    endTime = endInput ? endInput.getAttribute('data-time-value') : '';
                }



                if (!location || !bookingDate || !startTime || !endTime) {
                    showErrorMessage('Please fill in all required fields: location, date, start time, and end time.');
                    showBookingLoadingState(false);
                    return;
                }

                // Calculate duration
                const actualDuration = calculateDurationForBooking(startTime, endTime);
                const billingDuration = calculateBillingDuration(actualDuration);

                if (!endTime) {
                    showErrorMessage('Please select an end time for your booking.');
                    showBookingLoadingState(false);
                    return;
                }

                // DISABLED - Let backend handle time validation
                // if (actualDuration <= 0) {
                //     showErrorMessage('End time must be after start time.');
                //     showBookingLoadingState(false);
                //     return;
                // }

                // Remove client_location since we're not using it
                formData.delete('client_location');

                // Combine date and start time
                const bookingDateTime = bookingDate + ' ' + startTime + ':00';
                formData.set('booking_date', bookingDateTime);
                formData.set('duration_hours', billingDuration); // Use billing duration for payment calculation
                formData.set('actual_duration_hours', actualDuration); // Send actual booking duration separately

                // Sending duration hours to server
                formData.delete('start_time');
                formData.delete('end_time');

                try {
                    // Check if this is an update or create operation
                    const isUpdate = window.updateBookingId;
                    const url = isUpdate ? `/booking/${window.updateBookingId}/update` : '/booking/create';
                    const method = isUpdate ? 'PUT' : 'POST';

                    // For update, use JSON instead of FormData
                    let requestOptions;
                    if (isUpdate) {
                        const jsonData = {
                            booking_date: formData.get('booking_date'),
                            duration_hours: formData.get('duration_hours'),
                            actual_duration_hours: formData.get('actual_duration_hours'),
                            location: formData.get('location'),
                            notes: formData.get('notes') || ''
                        };

                        requestOptions = {
                            method: method,
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            body: JSON.stringify(jsonData)
                        };
                    } else {
                        requestOptions = {
                            method: method,
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        };
                    }

                    const response = await fetch(url, requestOptions);



                    let data;
                    try {
                        data = await response.json();

                    } catch (parseError) {
                        console.error('Failed to parse response as JSON:', parseError);
                        const responseText = await response.text();
                        console.error('Raw response:', responseText);
                        throw new Error('Invalid response format from server');
                    }

                    if (data.success) {
                        currentBooking = data.booking;

                        // Hide loading state
                        showBookingLoadingState(false);

                        if (isUpdate) {
                            // Booking update successful - show success message and redirect
                            showSuccessMessage(data.message || 'Booking updated successfully!');

                            // If payment_breakdown is available, show detailed breakdown
                            if (data.payment_breakdown) {
                                showUpdatePaymentBreakdown({
                                    original_amount: data.payment_breakdown.original_amount,
                                    new_amount: data.payment_breakdown.new_amount,
                                    difference: data.payment_breakdown.difference_amount,
                                    original_duration: data.payment_breakdown.original_duration,
                                    new_duration: data.payment_breakdown.new_duration,
                                    wallet_balance: data.payment_breakdown.wallet_balance,
                                    wallet_usage: data.payment_breakdown.wallet_usage,
                                    online_payment_required: data.payment_breakdown.online_payment_required,
                                    refund_amount: data.payment_breakdown.refund_amount
                                });
                            }

                            // Clear the update parameters from URL
                            const newUrl = window.location.pathname;
                            window.history.replaceState({}, document.title, newUrl);

                            // Remove update notification
                            const updateNotification = document.querySelector('.mb-4.p-4.bg-blue-50');
                            if (updateNotification) {
                                updateNotification.remove();
                            }

                            // Clear the update booking ID
                            window.updateBookingId = null;

                            // Redirect to notifications page
                            setTimeout(() => {
                                window.location.href = '/notifications';
                            }, 1500);
                        } else {
                            // For new bookings, open Razorpay payment directly
                            openRazorpayDirectly(data.booking);
                        }
                    } else {
                        showBookingLoadingState(false);

                        // Check if this is a payment required response (not an error)
                        if (isUpdate && data.payment_required) {
                            // User needs to pay additional amount - open difference payment directly
                            const bookingData = data.booking || {id: window.updateBookingId};
                            openDifferencePayment(bookingData, data.amount_difference, data);
                            return;
                        }

                        // Show error message for actual errors
                        let errorMessage = data.message || 'Error processing booking. Please try again.';

                        // This section is removed - payment breakdown will be shown above Book Now button

                        // If there are validation errors, show them
                        if (data.errors) {
                            const errorList = Object.values(data.errors).flat();
                            errorMessage = 'Validation errors:\n• ' + errorList.join('\n• ');
                        }

                        showErrorMessage(errorMessage);
                    }
                } catch (error) {
                    showBookingLoadingState(false);
                    console.error('Booking creation error:', error);
                    showErrorMessage('Error creating booking: ' + error.message);
                }
            });

            // Fetch active bookings on page load
            fetchActiveBookings();

            // Enhanced gallery modal with navigation and booking system initialized successfully
        });

        // Fetch and display active bookings
        async function fetchActiveBookings() {
            try {
                const response = await fetch(`/booking/user-bookings/{{ $user->id }}`);
                const data = await response.json();

                if (data.success && data.bookings.length > 0) {
                    displayActiveBookings(data.bookings);
                } else {
                    hideActiveBookings();
                }
            } catch (error) {
                console.error('Error fetching active bookings:', error);
                hideActiveBookings();
            }
        }

        // Display active bookings
        function displayActiveBookings(bookings) {
            const activeBookingsSection = document.getElementById('activeBookingsSection');
            const activeBookingsList = document.getElementById('activeBookingsList');
            const activeBookingsFormSection = document.getElementById('activeBookingsFormSection');
            const activeBookingsFormList = document.getElementById('activeBookingsFormList');
            const acceptedBookingStatus = document.getElementById('acceptedBookingStatus');
            const acceptedBookingDetails = document.getElementById('acceptedBookingDetails');

            // Filter bookings that haven't ended yet OR are cancelled (cancelled bookings can be updated)
            const currentBookings = bookings.filter(booking => {
                // Always include cancelled bookings regardless of time
                if (booking.status === 'cancelled') {
                    return true;
                }

                // For non-cancelled bookings, check if they haven't ended yet
                const bookingStartTime = new Date(booking.booking_date);
                const actualDuration = booking.actual_duration_hours || booking.duration_hours;
                const bookingEndTime = new Date(bookingStartTime.getTime() + (actualDuration * 60 * 60 * 1000));
                return bookingEndTime > new Date();
            });

            // Check for accepted/confirmed bookings
            const acceptedBookings = currentBookings.filter(booking =>
                booking.status === 'accepted' || booking.status === 'confirmed'
            );

            // Check for cancelled bookings
            const cancelledBookings = currentBookings.filter(booking =>
                booking.status === 'cancelled'
            );

            // If we have accepted bookings, show them in the top section
            if (acceptedBookings.length > 0) {
                const acceptedBooking = acceptedBookings[0]; // Show first accepted booking
                displayAcceptedBookingStatus(acceptedBooking);
                acceptedBookingStatus.classList.remove('hidden');
            } else {
                acceptedBookingStatus.classList.add('hidden');
            }

            // If we have cancelled bookings, always show them in the form section for updates
            if (cancelledBookings.length > 0) {
                // Show only cancelled bookings in the form section
                const bookingHTML = cancelledBookings.map(booking => generateBookingHTML(booking)).join('');
                activeBookingsList.innerHTML = bookingHTML;
                activeBookingsFormList.innerHTML = cancelledBookings.map(booking => generateDetailedBookingHTML(booking)).join('');

                // Show sections
                activeBookingsSection.classList.remove('hidden');
                activeBookingsFormSection.classList.remove('hidden');
                return;
            }

            // If we have accepted bookings but no cancelled bookings, hide the form section
            if (acceptedBookings.length > 0 && cancelledBookings.length === 0) {
                activeBookingsSection.classList.add('hidden');
                activeBookingsFormSection.classList.add('hidden');
                return;
            }

            if (currentBookings.length === 0) {
                hideActiveBookings();
                return;
            }

            // Generate HTML for both sections
            const bookingHTML = currentBookings.map(booking => generateBookingHTML(booking)).join('');

            // Update both sections
            activeBookingsList.innerHTML = bookingHTML;
            activeBookingsFormList.innerHTML = currentBookings.map(booking => generateDetailedBookingHTML(booking)).join('');

            // Show sections
            activeBookingsSection.classList.remove('hidden');
            activeBookingsFormSection.classList.remove('hidden');

            // Hide accepted booking status
            acceptedBookingStatus.classList.add('hidden');
        }

        // Display accepted booking status
        function displayAcceptedBookingStatus(booking) {
            const acceptedBookingStatus = document.getElementById('acceptedBookingStatus');
            const acceptedBookingDetails = document.getElementById('acceptedBookingDetails');

            // Store booking ID for chat function
            window.currentAcceptedBookingId = booking.id;

            // Calculate start and end times from booking_date and actual duration
            const bookingDate = new Date(booking.booking_date);
            const actualDuration = booking.actual_duration_hours || booking.duration_hours;
            const endTime = new Date(bookingDate.getTime() + (actualDuration * 60 * 60 * 1000));

            const html = `
                <div class="text-sm space-y-2">
                    <div class="flex justify-between">
                        <span class="text-green-700 font-medium">Date:</span>
                        <span class="text-green-800">${formatDate(booking.booking_date)}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-green-700 font-medium">Time:</span>
                        <span class="text-green-800">${formatTime12Hour(bookingDate)} - ${formatTime12Hour(endTime)}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-green-700 font-medium">Duration:</span>
                        <span class="text-green-800">${formatBookingDuration(booking.actual_duration_hours || booking.duration_hours || 0)}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-green-700 font-medium">Amount:</span>
                        <span class="text-green-800 font-semibold">₹${booking.total_amount || 0}</span>
                    </div>
                </div>
            `;

            acceptedBookingDetails.innerHTML = html;
            acceptedBookingStatus.classList.remove('hidden');
        }

        // Open chat from profile page
        function openChatFromProfile() {
            if (window.currentAcceptedBookingId) {
                console.log('Opening chat from profile for booking ID:', window.currentAcceptedBookingId);

                fetch(`/booking/${window.currentAcceptedBookingId}/chat-details`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                    .then(response => {
                        console.log('Profile chat response status:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Profile chat response data:', data);
                        if (data.success && data.chat_user_id) {
                            console.log('Redirecting to chat with user:', data.chat_user_id);
                            window.location.href = `/chat/${data.chat_user_id}`;
                        } else {
                            alert(data.message || 'Unable to open chat. Please try again.');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred: ' + error.message);
                    });
            } else {
                alert('No active booking found.');
            }
        }

        // Generate compact booking HTML for top section
        function generateBookingHTML(booking) {
            const statusColor = getStatusColor(booking.status);
            const statusText = getStatusText(booking.status);

            // Calculate start and end times from booking_date and actual duration
            const bookingDate = new Date(booking.booking_date);
            const actualDuration = booking.actual_duration_hours || booking.duration_hours;
            const endTime = new Date(bookingDate.getTime() + (actualDuration * 60 * 60 * 1000));

            return `
                <div class="bg-white/80 rounded-lg p-3 border border-gray-200">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-gray-800">${formatDate(booking.booking_date)}</span>
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${statusColor}">${statusText}</span>
                    </div>
                    <div class="text-xs text-gray-600">
                        ${formatTime12Hour(bookingDate)} - ${formatTime12Hour(endTime)}
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                        ₹${booking.total_amount || 0} • ${formatBookingDuration(booking.actual_duration_hours || booking.duration_hours || 0)}
                    </div>
                </div>
            `;
        }

        // Generate detailed booking HTML for form section
        function generateDetailedBookingHTML(booking) {
            const statusColor = getStatusColor(booking.status);
            const statusText = getStatusText(booking.status);

            // Calculate start and end times from booking_date and actual duration
            const bookingDate = new Date(booking.booking_date);
            const actualDuration = booking.actual_duration_hours || booking.duration_hours;
            const endTime = new Date(bookingDate.getTime() + (actualDuration * 60 * 60 * 1000));

            return `
                <div class="bg-white/80 rounded-xl p-4 border border-gray-200 shadow-sm">
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <h4 class="font-semibold text-gray-800">${formatDate(booking.booking_date)}</h4>
                            <p class="text-sm text-gray-600">${formatTime12Hour(bookingDate)} - ${formatTime12Hour(endTime)}</p>
                        </div>
                        <span class="px-3 py-1 text-sm font-medium rounded-full ${statusColor}">${statusText}</span>
                    </div>

                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-500">Duration:</span>
                            <span class="font-medium text-gray-800">${formatBookingDuration(booking.actual_duration_hours || booking.duration_hours || 0)}</span>
                        </div>
                        <div>
                            <span class="text-gray-500">Amount:</span>
                            <span class="font-medium text-gray-800">₹${booking.total_amount || 0}</span>
                        </div>
                        <div>
                            <span class="text-gray-500">Payment:</span>
                            <span class="font-medium text-green-600">${booking.payment_method || 'Paid'}</span>
                        </div>
                        <div>
                            <span class="text-gray-500">Booking ID:</span>
                            <span class="font-medium text-gray-800">#${booking.id}</span>
                        </div>
                    </div>

                    ${booking.status === 'pending' || booking.status === 'confirmed' ? `
                        <div class="mt-3 p-2 bg-yellow-50 rounded-lg border border-yellow-200">
                            <p class="text-xs text-yellow-700">
                                <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Waiting for provider to accept your booking request
                            </p>
                        </div>
                    ` : ''}

                    ${booking.status === 'accepted' ? `
                        <div class="mt-3 p-2 bg-green-50 rounded-lg border border-green-200">
                            <p class="text-xs text-green-700">
                                <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Booking confirmed! Provider has accepted your request
                            </p>
                        </div>
                    ` : ''}

                    ${booking.status === 'cancelled' ? `
                        <div class="mt-3 p-2 bg-red-50 rounded-lg border border-red-200">
                            <div class="flex justify-between items-center">
                                <p class="text-xs text-red-700">
                                    <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    This booking was cancelled
                                </p>
                                <button onclick="showUpdateBookingModal(${booking.id})"
                                        class="px-2 py-1 text-xs font-medium text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 rounded border border-blue-200 transition-colors">
                                    <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                    Update Booking
                                </button>
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // Helper functions
        function getStatusColor(status) {
            switch(status) {
                case 'pending': return 'bg-yellow-100 text-yellow-800';
                case 'confirmed': return 'bg-blue-100 text-blue-800';
                case 'accepted': return 'bg-green-100 text-green-800';
                case 'cancelled': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getStatusText(status) {
            switch(status) {
                case 'pending': return 'Waiting';
                case 'confirmed': return 'Waiting';
                case 'accepted': return 'Accepted';
                case 'cancelled': return 'Cancelled';
                default: return status;
            }
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);

            if (date.toDateString() === today.toDateString()) {
                return 'Today';
            } else if (date.toDateString() === tomorrow.toDateString()) {
                return 'Tomorrow';
            } else {
                return date.toLocaleDateString('en-IN', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                });
            }
        }

        function formatBookingDuration(durationHours) {
            if (!durationHours || durationHours <= 0) {
                return '0 hours';
            }

            const hours = Math.floor(durationHours);
            const minutes = Math.round((durationHours - hours) * 60);

            if (hours === 0) {
                return `${minutes} minutes`;
            } else if (minutes === 0) {
                return hours === 1 ? '1 hour' : `${hours} hours`;
            } else {
                return hours === 1 ? `1 hour ${minutes} min` : `${hours} hours ${minutes} min`;
            }
        }

        function calculateDuration(startTime, endTime) {
            try {
                // Ensure we have valid time strings
                if (!startTime || !endTime) {
                    return '0 hours';
                }

                // Parse time strings (HH:MM format)
                const startParts = startTime.split(':');
                const endParts = endTime.split(':');

                if (startParts.length !== 2 || endParts.length !== 2) {
                    return '0 hours';
                }

                const startHour = parseInt(startParts[0], 10);
                const startMin = parseInt(startParts[1], 10);
                const endHour = parseInt(endParts[0], 10);
                const endMin = parseInt(endParts[1], 10);

                // Check for valid numbers
                if (isNaN(startHour) || isNaN(startMin) || isNaN(endHour) || isNaN(endMin)) {
                    return '0 hours';
                }

                // Calculate total minutes
                const startTotalMin = startHour * 60 + startMin;
                const endTotalMin = endHour * 60 + endMin;
                const diffMin = endTotalMin - startTotalMin;

                if (diffMin <= 0) {
                    return '0 hours';
                }

                const hours = Math.floor(diffMin / 60);
                const minutes = diffMin % 60;

                if (hours === 0) {
                    return `${minutes} minutes`;
                } else if (minutes === 0) {
                    return hours === 1 ? '1 hour' : `${hours} hours`;
                } else {
                    return hours === 1 ? `1 hour ${minutes} min` : `${hours} hours ${minutes} min`;
                }
            } catch (error) {
                console.error('Error calculating duration:', error);
                return '0 hours';
            }
        }

        function hideActiveBookings() {
            const activeBookingsSection = document.getElementById('activeBookingsSection');
            const activeBookingsFormSection = document.getElementById('activeBookingsFormSection');
            const acceptedBookingStatus = document.getElementById('acceptedBookingStatus');

            activeBookingsSection.classList.add('hidden');
            activeBookingsFormSection.classList.add('hidden');
            acceptedBookingStatus.classList.add('hidden');
        }

        // Date change handler for availability checking
        document.addEventListener('DOMContentLoaded', function() {
            const bookingDateInput = document.getElementById('booking_date');
            if (bookingDateInput) {
                bookingDateInput.addEventListener('change', function() {
                    const selectedDate = this.value;
                    if (selectedDate) {
                        fetchAvailableTimeSlots(selectedDate);
                    } else {
                        hideAllTimeMessages();
                        showNoScheduleMessage();
                    }
                });
            }

            // Set current time as default for start time input
            const startTimeInput = document.getElementById('start_time');
            if (startTimeInput) {
                const now = new Date();
                const currentHour = now.getHours().toString().padStart(2, '0');
                const currentMinute = now.getMinutes().toString().padStart(2, '0');
                const currentTime = `${currentHour}:${currentMinute}`;
                startTimeInput.value = currentTime;
            }

            // Show initial message
            showNoScheduleMessage();
        });

        // Fetch available time slots for selected date
        async function fetchAvailableTimeSlots(selectedDate) {
            try {
                const response = await fetch(`/user/{{ $user->id }}/availability?date=${selectedDate}`, {
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    if (data.is_holiday) {
                        showHolidayMessage();
                    } else if (data.available_hours) {
                        showAvailableTimeSlots(selectedDate, data.available_hours, data.available_slots, data.booked_slots);
                    } else {
                        showNoScheduleMessage();
                    }
                } else {
                    showNoScheduleMessage();
                }
            } catch (error) {
                console.error('Error fetching availability:', error);
                showNoScheduleMessage();
            }
        }

        // Show available time slots
        function showAvailableTimeSlots(date, availableHours, availableSlots, bookedSlots) {
            hideAllTimeMessages();

            // Format date for display
            const dateObj = new Date(date);
            const formattedDate = dateObj.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            document.getElementById('selectedDateDisplay').textContent = formattedDate;

            // Display available time slots
            if (availableSlots && availableSlots.length > 0) {
                let availableHoursText = 'Available Hours: ';
                if (availableSlots.length === 1 && !bookedSlots.length) {
                    // No bookings, show full range
                    availableHoursText += availableSlots[0].display;
                } else {
                    // Show available slots with gaps
                    availableHoursText += availableSlots.map(slot => slot.display).join(', ');
                }
                document.getElementById('availableHoursDisplay').textContent = availableHoursText;
            } else {
                document.getElementById('availableHoursDisplay').textContent = 'No available time slots';
            }

            document.getElementById('availableTimesContainer').classList.remove('hidden');
            document.getElementById('timeInputsContainer').classList.remove('hidden');

            // Store availability hours globally for use in other functions
            window.currentAvailableHours = availableHours;

            // Create searchable time dropdowns with 15-minute intervals
            const startTimeDropdown = createSearchableTimeDropdown('start_time', 'start_time_dropdown', availableHours);
            const endTimeDropdown = createSearchableTimeDropdown('end_time', 'end_time_dropdown', availableHours);

            // Store dropdown instances for later use
            window.startTimeDropdown = startTimeDropdown;
            window.endTimeDropdown = endTimeDropdown;

            // Set start time to current time (rounded to next 15 minutes) if not already set
            if (!startTimeDropdown.getValue()) {
                const now = new Date();
                const currentHour = now.getHours();
                const currentMinute = now.getMinutes();

                // Round to next 15-minute interval (0, 15, 30, 45)
                let roundedMinute = Math.ceil(currentMinute / 15) * 15;
                let adjustedHour = currentHour;

                // If minutes rounded to 60, add an hour and reset minutes to 0
                if (roundedMinute >= 60) {
                    adjustedHour = currentHour + 1;
                    roundedMinute = 0;
                }

                // Format time as HH:MM for dropdown value (24-hour format)
                const formattedTime = String(adjustedHour).padStart(2, '0') + ':' + String(roundedMinute).padStart(2, '0');

                // Check if current time is within available hours and allows at least 15 minutes
                const endTimeLimit = subtractMinutesFromTime(availableHours.end_time, 15);

                if (formattedTime >= availableHours.start_time && formattedTime <= endTimeLimit) {
                    startTimeDropdown.setValue(formattedTime);

                } else {
                    // If current time is outside available hours, set to first available option
                    const allOptions = generateTimeOptions();
                    const availableOptions = filterTimeOptionsByAvailability(allOptions, availableHours, true);

                    if (availableOptions.length > 0) {
                        startTimeDropdown.setValue(availableOptions[0].value);
                        console.log('Set start time to first available:', availableOptions[0].value);
                    } else {
                        console.log('No available start times found');
                    }
                }
            }

            // Always clear end time (leave it blank for user selection)
            endTimeDropdown.clear();

            // Update the end time options based on start time
            updateEndTimeOptionsForSearchable(startTimeDropdown.getValue());

            // Add event listeners for dropdown changes
            document.getElementById('start_time').addEventListener('change', function() {
                updateEndTimeOptionsForSearchable(startTimeDropdown.getValue());
                updateTotalAmount();
            });

            document.getElementById('end_time').addEventListener('change', function() {
                updateTotalAmount();
            });

            // Update total amount display
            updateTotalAmount();

            // Set default values - prioritize current time if within available hours
            // But only if we're not in update booking mode (preserve pre-filled values)
            if (!window.updateBookingId) {
                // Get current time
                const now = new Date();
                const currentHour = now.getHours().toString().padStart(2, '0');
                const currentMinute = now.getMinutes().toString().padStart(2, '0');
                const currentTime = `${currentHour}:${currentMinute}`;

                // Check if current time is within available hours
                const isCurrentTimeAvailable = currentTime >= availableHours.start_time && currentTime <= availableHours.end_time;

                // This logic is now handled by the earlier 15-minute interval logic
                // We don't need to set times here as they're already set above with proper rounding
                // Just ensure end time stays blank for user selection
                endTimeDropdown.clear();
            } else {
                // Restore original times for update booking mode
                if (window.originalStartTime) {
                    startTimeDropdown.setValue(window.originalStartTime);
                }
                if (window.originalEndTime) {
                    endTimeDropdown.setValue(window.originalEndTime);
                }
            }

            // Trigger amount calculation after setting default times
            // Use setTimeout to ensure DOM is updated
            setTimeout(() => {
                updateTotalAmount();
            }, 100);
        }

        // Show holiday message
        function showHolidayMessage() {
            hideAllTimeMessages();
            document.getElementById('holidayMessage').classList.remove('hidden');
        }

        // Show no schedule message
        function showNoScheduleMessage() {
            hideAllTimeMessages();
            document.getElementById('noScheduleMessage').classList.remove('hidden');
        }

        // Hide all time-related messages
        function hideAllTimeMessages() {
            document.getElementById('availableTimesContainer').classList.add('hidden');
            document.getElementById('holidayMessage').classList.add('hidden');
            document.getElementById('noScheduleMessage').classList.add('hidden');
            document.getElementById('timeInputsContainer').classList.add('hidden');
            document.getElementById('durationDisplay').classList.add('hidden');

            // Reset time inputs and amount calculation
            if (window.startTimeDropdown) {
                window.startTimeDropdown.clear();
            } else {
                const startTimeInput = document.getElementById('start_time');
                if (startTimeInput) startTimeInput.value = '';
            }

            if (window.endTimeDropdown) {
                window.endTimeDropdown.clear();
            } else {
                const endTimeInput = document.getElementById('end_time');
                if (endTimeInput) endTimeInput.value = '';
            }

            // Reset amount display (totalAmount and totalHours elements removed)
            document.getElementById('calculatedDuration').textContent = '0 hours';
        }

        // Format time to 12-hour format
        function formatTime12Hour(time24) {
            // Handle null or undefined values
            if (!time24) {
                return 'Invalid Time';
            }

            // Handle Date objects
            if (time24 instanceof Date) {
                const hours = time24.getHours();
                const minutes = time24.getMinutes();
                const ampm = hours >= 12 ? 'PM' : 'AM';
                const hour12 = hours % 12 || 12;
                return `${hour12}:${minutes.toString().padStart(2, '0')} ${ampm}`;
            }

            // Handle string values (time strings or date strings)
            if (typeof time24 === 'string') {
                // Try to parse as date string first
                const dateObj = new Date(time24);
                if (!isNaN(dateObj.getTime())) {
                    const hours = dateObj.getHours();
                    const minutes = dateObj.getMinutes();
                    const ampm = hours >= 12 ? 'PM' : 'AM';
                    const hour12 = hours % 12 || 12;
                    return `${hour12}:${minutes.toString().padStart(2, '0')} ${ampm}`;
                }

                // Handle time strings (HH:MM format)
                const timeParts = time24.split(':');
                if (timeParts.length >= 2) {
                    const hours = parseInt(timeParts[0]);
                    const minutes = parseInt(timeParts[1]) || 0;
                    if (!isNaN(hours) && hours >= 0 && hours <= 23) {
                        const ampm = hours >= 12 ? 'PM' : 'AM';
                        const hour12 = hours % 12 || 12;
                        return `${hour12}:${minutes.toString().padStart(2, '0')} ${ampm}`;
                    }
                }
            }

            return 'Invalid Time';
        }

        // Razorpay integration with enhanced error handling
        async function initializeRazorpay(booking) {
            try {
                const response = await fetch(`/booking/${booking.id}/payment`);
                const data = await response.json();

                if (data.success) {
                    const razorpayButton = document.getElementById('razorpayButton');

                    // Display payment breakdown if available
                    if (data.payment_breakdown) {
                        console.log('Payment breakdown received:', data.payment_breakdown);
                        displayPaymentBreakdown(data.payment_breakdown);
                    } else {
                        console.log('No payment breakdown in response:', data);
                    }

                    // Show remaining payment time if available
                    if (data.remaining_time && data.remaining_time > 0) {
                        showPaymentTimer(data.remaining_time);
                    }

                    // Update button text based on payment requirement
                    if (data.payment_breakdown && !data.payment_breakdown.payment_required) {
                        razorpayButton.textContent = 'Complete Booking (Paid from Wallet)';
                        razorpayButton.innerHTML = `
                            <span class="flex items-center justify-center space-x-3">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                <span class="text-lg">Complete Booking${data.payment_breakdown.wallet_usage > 0 ? ' (Wallet: ₹' + data.payment_breakdown.wallet_usage + ')' : ''}</span>
                            </span>
                        `;
                    } else if (data.payment_breakdown) {
                        razorpayButton.innerHTML = `
                            <span class="flex items-center justify-center space-x-3">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                                <span class="text-lg">Pay ₹${data.payment_breakdown.razorpay_amount} ${data.payment_breakdown.wallet_usage > 0 ? '(+ ₹' + data.payment_breakdown.wallet_usage + ' from Wallet)' : ''}</span>
                            </span>
                        `;
                    }

                    razorpayButton.onclick = async function() {
                        console.log('Payment button clicked');
                        console.log('Payment breakdown:', data.payment_breakdown);

                        // Handle wallet-only payment
                        if (data.payment_breakdown && !data.payment_breakdown.payment_required) {
                            console.log('Processing wallet-only payment');
                            processWalletOnlyPayment(booking.id);
                            return;
                        }

                        console.log('Processing Razorpay payment for amount:', data.payment_breakdown ? data.payment_breakdown.razorpay_amount : booking.total_amount);

                        // Always use real Razorpay payment - no test mode simulation
                        console.log('Processing Razorpay payment for amount:', data.payment_breakdown ? data.payment_breakdown.razorpay_amount : booking.total_amount);

                        // Real Razorpay payment
                        const paymentAmount = booking.is_update_payment ? booking.additional_payment :
                                             (data.payment_breakdown ? data.payment_breakdown.razorpay_amount : booking.total_amount);

                        // Debug Razorpay configuration
                        console.log('Razorpay Key:', data.razorpay_key);
                        // Payment configuration ready

                        // Always open Razorpay payment dialog - no test mode bypass

                        const options = {
                            key: data.razorpay_key,
                            amount: Math.round(paymentAmount * 100), // Amount in paise
                            currency: "INR",
                            name: "Dating App",
                            description: booking.is_update_payment ?
                                       `Additional Payment for Booking Update` :
                                       `Time Spending with {{ $user->name }}`,
                            image: "{{ asset('images/logo.png') }}",
                            order_id: data.razorpay_order_id,
                            handler: async function (response) {
                                try {
                                    const paymentResponse = await fetch('/booking/process-payment', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json',
                                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                        },
                                        body: JSON.stringify({
                                            booking_id: booking.id,
                                            razorpay_payment_id: response.razorpay_payment_id,
                                            razorpay_order_id: response.razorpay_order_id,
                                            razorpay_signature: response.razorpay_signature
                                        })
                                    });

                                    const paymentData = await paymentResponse.json();

                                    if (paymentData.success) {
                                        closePaymentModal();
                                        showSuccessMessage('Payment successful! Your booking is confirmed.');
                                        // Refresh availability after successful payment
                                        setTimeout(() => {
                                            window.location.reload();
                                        }, 2000);
                                    } else {
                                        if (paymentData.expired) {
                                            closePaymentModal();
                                            showErrorMessage('Your booking has expired. Please create a new booking.');
                                            // Refresh availability to show released time slot
                                            setTimeout(() => {
                                                window.location.reload();
                                            }, 3000);
                                        } else {
                                            showErrorMessage(paymentData.message || 'Payment processing failed. Please try again.');
                                        }
                                    }
                                } catch (error) {
                                    console.error('Error processing payment:', error);
                                    showErrorMessage('Payment processing failed. Please try again.');
                                }
                            },
                            prefill: {
                                name: "{{ Auth::user()->name }}",
                                email: "{{ Auth::user()->email }}",
                                contact: "{{ Auth::user()->contact_number ?? '' }}"
                            },
                            theme: {
                                color: "#EC4899"
                            },
                            modal: {
                                ondismiss: function() {
                                    console.log('Razorpay modal dismissed');
                                    // Handle payment cancellation
                                    handlePaymentCancellation(booking.id);
                                }
                            }
                        };

                        try {
                            const rzp = new Razorpay(options);
                            rzp.open();
                        } catch (razorpayError) {
                            showErrorMessage('Error opening payment gateway: ' + razorpayError.message);
                        }
                    }; // Missing closing brace for razorpayButton.onclick function
                } else {
                    if (data.expired) {
                        showErrorMessage('This booking has expired. Please create a new booking.');
                        // Refresh to show updated availability
                        setTimeout(() => {
                            window.location.reload();
                        }, 3000);
                    } else if (data.cancelled) {
                        showErrorMessage('This booking has been cancelled.');
                    } else {
                        showErrorMessage('Error initializing payment: ' + (data.message || 'Please try again.'));
                    }
                }
            } catch (error) {
                showErrorMessage('Error initializing payment: ' + error.message);
            }
        }

        // Payment timer functionality
        let paymentTimer = null;
        function showPaymentTimer(remainingMinutes) {
            const timerElement = document.getElementById('paymentTimer');
            if (!timerElement) return;

            // Show the timer element
            timerElement.classList.remove('hidden');

            let timeLeft = remainingMinutes * 60; // Convert to seconds

            paymentTimer = setInterval(() => {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;

                // Update timer display with color coding
                if (timeLeft <= 300) { // Last 5 minutes - red warning
                    timerElement.className = 'text-center text-sm text-red-600 font-bold bg-red-50 rounded-lg p-3 border border-red-200';
                    timerElement.innerHTML = `
                        <div class="flex items-center justify-center space-x-2">
                            <svg class="w-4 h-4 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <span>Payment expires in: ${minutes}:${seconds.toString().padStart(2, '0')}</span>
                        </div>
                    `;
                } else if (timeLeft <= 600) { // Last 10 minutes - orange warning
                    timerElement.className = 'text-center text-sm text-orange-600 font-medium bg-orange-50 rounded-lg p-3 border border-orange-200';
                    timerElement.innerHTML = `
                        <div class="flex items-center justify-center space-x-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>Payment expires in: ${minutes}:${seconds.toString().padStart(2, '0')}</span>
                        </div>
                    `;
                } else { // Normal timer - blue
                    timerElement.className = 'text-center text-sm text-blue-600 font-medium bg-blue-50 rounded-lg p-3';
                    timerElement.innerHTML = `
                        <div class="flex items-center justify-center space-x-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>Payment expires in: ${minutes}:${seconds.toString().padStart(2, '0')}</span>
                        </div>
                    `;
                }

                if (timeLeft <= 0) {
                    clearInterval(paymentTimer);
                    timerElement.className = 'text-center text-sm text-red-600 font-bold bg-red-100 rounded-lg p-3 border border-red-300';
                    timerElement.innerHTML = `
                        <div class="flex items-center justify-center space-x-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            <span>Payment expired</span>
                        </div>
                    `;
                    showErrorMessage('Payment time has expired. Please create a new booking.');
                    setTimeout(() => {
                        window.location.reload();
                    }, 3000);
                }

                timeLeft--;
            }, 1000);
        }

        // Success and error message functions
        function showSuccessMessage(message) {
            // Create or update success message element
            let messageEl = document.getElementById('successMessage');
            if (!messageEl) {
                messageEl = document.createElement('div');
                messageEl.id = 'successMessage';
                messageEl.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg';
                messageEl.style.zIndex = '100000';
                document.body.appendChild(messageEl);
            }
            messageEl.textContent = message;
            messageEl.style.display = 'block';

            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 5000);
        }

        function showErrorMessage(message) {
            // Create or update error message element
            let messageEl = document.getElementById('errorMessage');
            if (!messageEl) {
                messageEl = document.createElement('div');
                messageEl.id = 'errorMessage';
                messageEl.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg';
                messageEl.style.zIndex = '100000';
                document.body.appendChild(messageEl);
            }
            messageEl.textContent = message;
            messageEl.style.display = 'block';

            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 5000);
        }

        // Handle payment cancellation
        async function handlePaymentCancellation(bookingId) {
            try {
                const response = await fetch(`/booking/${bookingId}/cancel`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        reason: 'Payment cancelled by user'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showSuccessMessage('Booking cancelled. The time slot is now available for others.');
                    // Refresh to show updated availability
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    // Booking cancellation completed
                }
            } catch (error) {
                console.error('Error cancelling booking:', error);
            }
        }

        // Show/hide booking loading state
        function showBookingLoadingState(isLoading) {
            const button = document.getElementById('bookNowButton');
            const content = document.getElementById('bookNowButtonContent');
            const loading = document.getElementById('bookNowButtonLoading');

            if (isLoading) {
                button.disabled = true;
                content.classList.add('hidden');
                loading.classList.remove('hidden');
            } else {
                button.disabled = false;
                content.classList.remove('hidden');
                loading.classList.add('hidden');
            }
        }

        // Update payment modal content with booking details
        function updatePaymentModalContent(booking) {
            // Update amount - will be overridden by payment breakdown if available
            document.getElementById('paymentAmount').textContent = '₹' + booking.total_amount.toLocaleString();

            // Update modal title with booking details
            const modalHeader = document.querySelector('#paymentModal h3');
            if (modalHeader) {
                modalHeader.textContent = 'Complete Payment';
            }

            // Add booking summary
            const bookingSummary = document.getElementById('bookingSummary');
            if (bookingSummary) {
                const bookingDate = new Date(booking.booking_date);
                const formattedDate = bookingDate.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                const formattedTime = bookingDate.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                });

                const actualDuration = booking.actual_duration_hours || booking.duration_hours;
                const endTime = new Date(bookingDate.getTime() + (actualDuration * 60 * 60 * 1000));
                const formattedEndTime = endTime.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                });

                bookingSummary.innerHTML = `
                    <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-200">
                        <div class="flex items-center mb-3">
                            <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            <span class="font-semibold text-gray-800">Booking Summary</span>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center text-gray-700">
                                <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <span class="font-medium">${formattedDate}</span>
                            </div>
                            <div class="flex items-center text-gray-700">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>${formattedTime} - ${formattedEndTime} (${formatBookingDuration(booking.actual_duration_hours || booking.duration_hours)})</span>
                            </div>
                            <div class="flex items-center text-gray-700">
                                <svg class="w-4 h-4 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span class="truncate">${booking.meeting_location}</span>
                            </div>
                            <div class="flex items-center text-gray-700">
                                <svg class="w-4 h-4 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span>Time with {{ $user->name }}</span>
                            </div>
                        </div>
                    </div>
                `;
                bookingSummary.classList.remove('hidden');
            }
        }

        // Enhanced payment modal opening with booking context
        function openPaymentModalWithBooking(booking) {
            updatePaymentModalContent(booking);
            openPaymentModal();
            initializeRazorpay(booking);
        }

        // Handle booking cancellation from payment modal
        async function handleBookingCancellation() {
            if (!currentBooking) {
                showErrorMessage('No active booking to cancel.');
                return;
            }

            // Show confirmation dialog
            if (!confirm('Are you sure you want to cancel this booking? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`/booking/${currentBooking.id}/cancel`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        reason: 'Cancelled by user from payment modal'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    closePaymentModal();
                    showSuccessMessage('Booking cancelled successfully. The time slot is now available for others.');

                    // Clear current booking
                    currentBooking = null;

                    // Refresh availability to show released time slot
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    showErrorMessage(data.message || 'Failed to cancel booking. Please try again.');
                }
            } catch (error) {
                console.error('Error cancelling booking:', error);
                showErrorMessage('Error cancelling booking. Please try again.');
            }
        }

        // Direct Razorpay opening without modal
        async function openRazorpayDirectly(booking) {
            try {
                const response = await fetch(`/booking/${booking.id}/payment`);
                const data = await response.json();

                if (data.success) {
                    // Check if wallet-only payment
                    if (data.payment_breakdown && !data.payment_breakdown.payment_required) {
                        processWalletOnlyPayment(booking.id);
                        return;
                    }

                    // Real Razorpay payment
                    const paymentAmount = booking.is_update_payment ? booking.additional_payment :
                                         (data.payment_breakdown ? data.payment_breakdown.razorpay_amount : booking.total_amount);

                    // Always open Razorpay payment dialog - no test mode bypass

                    const options = {
                        key: data.razorpay_key,
                        amount: Math.round(paymentAmount * 100), // Amount in paise
                        currency: "INR",
                        name: "Dating App",
                        description: booking.is_update_payment ?
                                   `Additional Payment for Booking Update` :
                                   `Time Spending with {{ $user->name }}`,
                        image: "{{ asset('images/logo.png') }}",
                        order_id: data.razorpay_order_id, // Use the order ID from backend
                        timeout: 300, // 5 minutes timeout
                        retry: {
                            enabled: true,
                            max_count: 3
                        },
                        handler: async function (response) {
                                try {
                                    const paymentResponse = await fetch('/booking/process-payment', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json',
                                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                        },
                                        body: JSON.stringify({
                                            booking_id: booking.id,
                                            razorpay_payment_id: response.razorpay_payment_id,
                                            razorpay_order_id: response.razorpay_order_id,
                                            razorpay_signature: response.razorpay_signature
                                        })
                                    });

                                    const paymentData = await paymentResponse.json();

                                    if (paymentData.success) {
                                        showSuccessMessage('Payment successful! Your booking is confirmed.');
                                        // Refresh active bookings and availability after successful payment
                                        fetchActiveBookings();
                                        setTimeout(() => {
                                            window.location.reload();
                                        }, 2000);
                                    } else {
                                        if (paymentData.expired) {
                                            showErrorMessage('Your booking has expired. Please create a new booking.');
                                            // Refresh availability to show released time slot
                                            setTimeout(() => {
                                                window.location.reload();
                                            }, 3000);
                                        } else {
                                            showErrorMessage(paymentData.message || 'Payment processing failed. Please try again.');
                                        }
                                    }
                                } catch (error) {
                                    console.error('Error processing payment:', error);
                                    showErrorMessage('Payment processing failed. Please try again.');
                                }
                        },
                        prefill: {
                            name: "{{ Auth::user()->name }}",
                            email: "{{ Auth::user()->email }}",
                            contact: "{{ Auth::user()->contact_number ?? '' }}"
                        },
                        theme: {
                            color: "#EC4899"
                        },
                        modal: {
                            ondismiss: function() {
                                console.log('Razorpay modal dismissed - cancelling booking');
                                // Cancel booking when payment is dismissed
                                handlePaymentCancellation(booking.id);
                            },
                            escape: true,
                            backdropclose: false,
                            handleback: true
                        },
                        config: {
                            display: {
                                blocks: {
                                    banks: {
                                        name: 'Pay using UPI or Bank Account',
                                        instruments: [
                                            {
                                                method: 'upi'
                                            },
                                            {
                                                method: 'netbanking'
                                            }
                                        ]
                                    },
                                    other: {
                                        name: 'Other Payment Methods',
                                        instruments: [
                                            {
                                                method: 'card'
                                            },
                                            {
                                                method: 'wallet'
                                            }
                                        ]
                                    }
                                },
                                sequence: ['block.banks', 'block.other'],
                                preferences: {
                                    show_default_blocks: true
                                }
                            }
                        }
                    };

                    try {
                        const rzp = new Razorpay(options);
                        rzp.open();
                    } catch (razorpayError) {
                        console.error('Error creating/opening direct Razorpay:', razorpayError);
                        showErrorMessage('Error opening payment gateway: ' + razorpayError.message);
                    }
                } else {
                    if (data.expired) {
                        showErrorMessage('This booking has expired. Please create a new booking.');
                        setTimeout(() => {
                            window.location.reload();
                        }, 3000);
                    } else if (data.cancelled) {
                        showErrorMessage('This booking has been cancelled.');
                    } else {
                        console.error('Payment initialization failed:', data);
                        showErrorMessage('Error initializing payment: ' + (data.message || 'Please try again.'));
                    }
                }
            } catch (error) {
                console.error('Error initializing Razorpay:', error);
                showErrorMessage('Error initializing payment: ' + error.message);
            }
        }
        // Display payment breakdown
        function displayPaymentBreakdown(breakdown) {
            const paymentBreakdownEl = document.getElementById('paymentBreakdown');
            if (!paymentBreakdownEl) return;

            let html = `
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-200 mb-4">
                    <div class="flex items-center mb-3">
                        <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        <span class="font-semibold text-gray-800">Payment Breakdown</span>
                    </div>
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between items-center py-2 border-b border-gray-200">
                            <span class="text-gray-700 font-medium">Total Booking Amount:</span>
                            <span class="font-bold text-gray-900 text-lg">₹${breakdown.total_amount}</span>
                        </div>
                        ${breakdown.wallet_balance > 0 ? `
                        <div class="flex justify-between items-center">
                            <span class="text-gray-700">Your Wallet Balance:</span>
                            <span class="font-semibold text-green-600">₹${breakdown.wallet_balance}</span>
                        </div>
                        ` : ''}
                        ${breakdown.wallet_usage > 0 ? `
                        <div class="bg-green-50 rounded-lg p-3 border border-green-200">
                            <div class="flex justify-between items-center">
                                <span class="text-green-700 font-medium">💰 Amount from Wallet:</span>
                                <span class="font-bold text-green-700 text-lg">₹${breakdown.wallet_usage}</span>
                            </div>
                        </div>
                        ` : ''}
                        ${breakdown.platform_fee && breakdown.platform_fee > 0 ? `
                        <div class="flex justify-between items-center">
                            <span class="text-gray-700">Platform Fee:</span>
                            <span class="font-medium text-gray-900">₹${breakdown.platform_fee}</span>
                        </div>
                        ` : ''}
            `;

            if (breakdown.payment_required) {
                html += `
                        <div class="bg-blue-50 rounded-lg p-3 border border-blue-200 mt-2">
                            <div class="flex justify-between items-center">
                                <span class="text-blue-700 font-medium">💳 Need to Pay (Razorpay):</span>
                                <span class="font-bold text-blue-700 text-lg">₹${breakdown.razorpay_amount}</span>
                            </div>
                            <div class="text-xs text-blue-600 mt-1">This amount will be charged via Razorpay</div>
                        </div>
                `;
            } else {
                html += `
                        <div class="bg-green-50 rounded-lg p-3 border border-green-200 mt-2">
                            <div class="flex justify-between items-center">
                                <span class="text-green-700 font-medium">✅ Remaining to Pay:</span>
                                <span class="font-bold text-green-700 text-lg">₹0</span>
                            </div>
                            <div class="text-xs text-green-600 mt-1">Fully paid from wallet - No additional payment needed!</div>
                        </div>
                `;
            }

            html += `
                    </div>
                </div>
            `;

            paymentBreakdownEl.innerHTML = html;
            paymentBreakdownEl.classList.remove('hidden');

            // Update main payment amount display
            const paymentAmountEl = document.getElementById('paymentAmount');
            if (paymentAmountEl) {
                if (breakdown.payment_required) {
                    paymentAmountEl.textContent = '₹' + breakdown.razorpay_amount.toLocaleString();
                } else {
                    paymentAmountEl.textContent = '₹0';
                }
            }
        }

        // Process wallet-only payment
        async function processWalletOnlyPayment(bookingId) {
            try {
                console.log('Processing wallet-only payment for booking:', bookingId);

                const response = await fetch('/booking/process-payment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        booking_id: bookingId
                        // No Razorpay details needed for wallet-only payment
                    })
                });

                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);

                if (data.success) {
                    closePaymentModal();
                    showSuccessPopup(data.booking);
                    // Refresh active bookings and availability after successful payment
                    fetchActiveBookings();
                } else {
                    if (data.expired) {
                        closePaymentModal();
                        showErrorMessage('Your booking has expired. Please create a new booking.');
                        setTimeout(() => {
                            window.location.reload();
                        }, 3000);
                    } else {
                        showErrorMessage(data.message || 'Payment processing failed. Please try again.');
                    }
                }
            } catch (error) {
                console.error('Error processing wallet payment:', error);
                showErrorMessage('Payment processing failed. Please try again.');
            }
        }

        // Success Popup Functions
        function showSuccessPopup(booking) {
            const modal = document.getElementById('successModal');
            const content = document.getElementById('successModalContent');
            const detailsContainer = document.getElementById('successBookingDetails');

            // Format booking details
            const bookingDate = new Date(booking.booking_date);
            const actualDuration = booking.actual_duration_hours || booking.duration_hours;
            const endTime = new Date(bookingDate.getTime() + (actualDuration * 60 * 60 * 1000));

            const detailsHTML = `
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex items-center mb-3">
                        <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <h3 class="font-semibold text-gray-800">Booking Details</h3>
                    </div>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Date:</span>
                            <span class="font-medium">${formatDate(booking.booking_date)}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Time:</span>
                            <span class="font-medium">${formatTime12Hour(bookingDate)} - ${formatTime12Hour(endTime)}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Duration:</span>
                            <span class="font-medium">${booking.duration_hours || 0} hour${(booking.duration_hours || 0) > 1 ? 's' : ''}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Amount:</span>
                            <span class="font-medium text-green-600">₹${booking.total_amount || 0}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Location:</span>
                            <span class="font-medium">${booking.meeting_location}</span>
                        </div>
                    </div>
                </div>
                <div class="bg-blue-50 rounded-lg p-4">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div class="text-sm text-blue-800">
                            <p class="font-medium mb-1">Next Steps:</p>
                            <p>Wait for {{ $user->name }} to accept your booking. You'll receive a notification once confirmed.</p>
                        </div>
                    </div>
                </div>
            `;

            detailsContainer.innerHTML = detailsHTML;

            // Show modal with animation
            modal.classList.remove('hidden');
            setTimeout(() => {
                content.classList.remove('scale-95', 'opacity-0');
                content.classList.add('scale-100', 'opacity-100');
            }, 50);
        }

        function closeSuccessModal() {
            const modal = document.getElementById('successModal');
            const content = document.getElementById('successModalContent');

            content.classList.remove('scale-100', 'opacity-100');
            content.classList.add('scale-95', 'opacity-0');

            setTimeout(() => {
                modal.classList.add('hidden');
                // Refresh the page to update booking status
                window.location.reload();
            }, 300);
        }

        function goToTransactions() {
            window.location.href = '/transactions';
        }

        // Enhanced booking status management
        function updateBookingStatusDisplay() {
            // Check if we're in update booking mode - if so, skip status display to avoid interference
            const urlParams = new URLSearchParams(window.location.search);
            const updateBookingId = urlParams.get('update_booking');

            if (updateBookingId) {
                console.log('Skipping booking status display - update booking mode active for booking ID:', updateBookingId);
                return;
            }

            const providerId = {{ $user->id ?? 0 }};

            fetch(`/booking/status/${providerId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayBookingStatus(data.bookings);
                    }
                })
                .catch(error => {
                    console.error('Error fetching booking status:', error);
                });
        }

        function displayBookingStatus(bookings) {
            const activeSection = document.getElementById('activeBookingsSection');
            const acceptedSection = document.getElementById('acceptedBookingStatus');
            const hireMeButton = document.getElementById('hireMeButton');

            // Hide all sections first
            activeSection.classList.add('hidden');
            acceptedSection.classList.add('hidden');

            const now = new Date();
            const activeBookings = [];
            const acceptedBookings = [];

            bookings.forEach(booking => {
                const bookingDate = new Date(booking.booking_date);
                const actualDuration = booking.actual_duration_hours || booking.duration_hours;
                const endTime = new Date(bookingDate.getTime() + (actualDuration * 60 * 60 * 1000));

                if (booking.provider_status === 'accepted' && now < endTime) {
                    acceptedBookings.push(booking);
                } else if (booking.provider_status === 'pending' && booking.payment_status === 'paid') {
                    activeBookings.push(booking);
                }
                // Note: Past bookings are no longer displayed here - they're available in the "My Bookings" tab
            });

            // Display active bookings (pending acceptance)
            if (activeBookings.length > 0) {
                displayActiveBookings(activeBookings);
                activeSection.classList.remove('hidden');
                hireMeButton.textContent = 'Booking Pending';
                hireMeButton.disabled = true;
                hireMeButton.classList.add('opacity-50', 'cursor-not-allowed');
            }
            // Display accepted bookings (current)
            else if (acceptedBookings.length > 0) {
                displayAcceptedBooking(acceptedBookings[0]);
                acceptedSection.classList.remove('hidden');
                hireMeButton.textContent = 'Currently Booked';
                hireMeButton.disabled = true;
                hireMeButton.classList.add('opacity-50', 'cursor-not-allowed');
            }
            // No active bookings - enable hire button
            else {
                hireMeButton.textContent = 'Hire Me';
                hireMeButton.disabled = false;
                hireMeButton.classList.remove('opacity-50', 'cursor-not-allowed');
            }
        }

        function displayActiveBookings(bookings) {
            const container = document.getElementById('activeBookingsList');
            const html = bookings.map(booking => {
                const bookingDate = new Date(booking.booking_date);
                const actualDuration = booking.actual_duration_hours || booking.duration_hours;
                const endTime = new Date(bookingDate.getTime() + (actualDuration * 60 * 60 * 1000));

                return `
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <div class="text-sm">
                            <div class="font-medium text-yellow-800 mb-1">
                                ${formatDate(booking.booking_date)}
                            </div>
                            <div class="text-yellow-700">
                                ${formatTime12Hour(bookingDate)} - ${formatTime12Hour(endTime)}
                            </div>
                            <div class="text-xs text-yellow-600 mt-1">
                                Waiting for acceptance
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            container.innerHTML = html;
        }

        function displayAcceptedBooking(booking) {
            const container = document.getElementById('acceptedBookingDetails');
            const bookingDate = new Date(booking.booking_date);
            const actualDuration = booking.actual_duration_hours || booking.duration_hours;
            const endTime = new Date(bookingDate.getTime() + (actualDuration * 60 * 60 * 1000));

            const html = `
                <div class="text-sm space-y-2">
                    <div class="flex justify-between">
                        <span class="text-green-700 font-medium">Date:</span>
                        <span class="text-green-800">${formatDate(booking.booking_date)}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-green-700 font-medium">Time:</span>
                        <span class="text-green-800">${formatTime12Hour(bookingDate)} - ${formatTime12Hour(endTime)}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-green-700 font-medium">Duration:</span>
                        <span class="text-green-800">${booking.duration_hours || 0} hour${(booking.duration_hours || 0) > 1 ? 's' : ''}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-green-700 font-medium">Amount:</span>
                        <span class="text-green-800">₹${booking.total_amount || 0}</span>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }



        // Initialize booking status on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateBookingStatusDisplay();

            // Update status every 30 seconds (but only if not in update booking mode)
            setInterval(() => {
                const urlParams = new URLSearchParams(window.location.search);
                const updateBookingId = urlParams.get('update_booking');

                if (!updateBookingId) {
                    updateBookingStatusDisplay();
                }
            }, 30000);
        });

        // Cleanup timer on page unload
        window.addEventListener('beforeunload', () => {
            if (paymentTimer) {
                clearInterval(paymentTimer);
            }
        });

        // Update Booking Modal Functions
        let currentUpdateBookingId = null;

        function showUpdateBookingModal(bookingId) {
            currentUpdateBookingId = bookingId;

            // Fetch booking details
            fetch(`/booking/${bookingId}/details`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const booking = data.booking;
                        const bookingDate = new Date(booking.booking_date);

                        // Populate modal with current booking details
                        document.getElementById('updateBookingDate').value = booking.booking_date.split('T')[0];
                        document.getElementById('updateBookingTime').value = bookingDate.toTimeString().slice(0, 5);
                        document.getElementById('updateDurationHours').value = booking.duration_hours;
                        document.getElementById('updateLocation').value = booking.meeting_location;

                        // Show current booking info
                        document.getElementById('currentBookingInfo').innerHTML = `
                            <div class="text-sm text-gray-600 mb-4">
                                <h4 class="font-medium text-gray-800 mb-2">Current Booking Details:</h4>
                                <div class="bg-gray-50 p-3 rounded-lg">
                                    <div class="grid grid-cols-2 gap-2 text-xs">
                                        <div><span class="font-medium">Date:</span> ${formatDate(booking.booking_date)}</div>
                                        <div><span class="font-medium">Time:</span> ${formatTime12Hour(bookingDate)}</div>
                                        <div><span class="font-medium">Duration:</span> ${booking.duration_hours || 0}h</div>
                                        <div><span class="font-medium">Amount:</span> ₹${booking.total_amount || 0}</div>
                                    </div>
                                </div>
                            </div>
                        `;

                        document.getElementById('updateBookingModal').classList.remove('hidden');
                    } else {
                        alert('Error loading booking details. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Error fetching booking details:', error);
                    alert('Error loading booking details. Please try again.');
                });
        }

        function closeUpdateBookingModal() {
            document.getElementById('updateBookingModal').classList.add('hidden');
            currentUpdateBookingId = null;
        }

        function calculateUpdateAmount() {
            const durationHours = parseFloat(document.getElementById('updateDurationHours').value) || 0;
            const hourlyRate = {{ floatval($user->hourly_rate ?? 0) }};
            const platformFeeAmount = {{ floatval(\App\Models\Setting::get('platform_fee', 0)) }};
            const baseAmount = durationHours * hourlyRate;
            const newAmount = baseAmount + platformFeeAmount;

            document.getElementById('updateTotalAmount').textContent = newAmount.toFixed(0);
            document.getElementById('updateTotalHours').textContent = durationHours === 1 ? '1 hour' : `${durationHours} hours`;
        }

        function submitUpdateBooking() {
            if (!currentUpdateBookingId) return;

            const updateBtn = document.getElementById('updateBookingBtn');
            const btnText = document.getElementById('updateBtnText');
            const btnLoading = document.getElementById('updateBtnLoading');

            // Show loading state
            updateBtn.disabled = true;
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');

            const bookingDate = document.getElementById('updateBookingDate').value;
            const bookingTime = document.getElementById('updateBookingTime').value;
            const durationHours = document.getElementById('updateDurationHours').value;
            const location = document.getElementById('updateLocation').value;

            // Combine date and time
            const bookingDateTime = `${bookingDate}T${bookingTime}:00`;

            fetch(`/booking/${currentUpdateBookingId}/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    booking_date: bookingDateTime,
                    duration_hours: durationHours,
                    location: location
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showSuccessMessage(data.message);

                    // Close modal
                    closeUpdateBookingModal();

                    // Refresh booking status and active bookings
                    updateBookingStatusDisplay();
                    fetchActiveBookings();
                } else {
                    if (data.errors) {
                        const errorMessages = Object.values(data.errors).flat().join('\n');
                        showErrorMessage('Validation errors:\n' + errorMessages);
                    } else {
                        showErrorMessage(data.message || 'Error updating booking. Please try again.');
                    }
                }
            })
            .catch(error => {
                showErrorMessage('Error updating booking. Please try again.');
            })
            .finally(() => {
                // Reset button state
                updateBtn.disabled = false;
                btnText.classList.remove('hidden');
                btnLoading.classList.add('hidden');
            });
        }

        // Add event listener for duration change
        document.addEventListener('DOMContentLoaded', function() {
            const durationInput = document.getElementById('updateDurationHours');
            if (durationInput) {
                durationInput.addEventListener('input', calculateUpdateAmount);
            }
        });

        // Function to check URL parameters and pre-fill form for booking update
        function checkAndPreFillUpdateBooking() {
            const urlParams = new URLSearchParams(window.location.search);
            const updateBookingId = urlParams.get('update_booking');

            if (updateBookingId) {
                // Check if notification already exists to prevent duplicates
                const existingNotification = document.querySelector('.mb-4.p-4.bg-blue-50');
                if (!existingNotification) {
                    // Show update booking notification
                    const updateNotification = document.createElement('div');
                    updateNotification.className = 'mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg';
                    updateNotification.innerHTML = `
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            <div>
                                <h4 class="text-sm font-medium text-blue-800">Updating Booking #${updateBookingId}</h4>
                                <p class="text-sm text-blue-600">Modify the details below and submit to update your booking request.</p>
                            </div>
                        </div>
                    `;

                    // Insert notification before the booking form
                    const bookingForm = document.querySelector('.booking-form-container');
                    if (bookingForm) {
                        bookingForm.parentNode.insertBefore(updateNotification, bookingForm);
                    }
                }

                // Fetch booking details from server for security
                fetch(`/booking/${updateBookingId}/details`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    credentials: 'same-origin'
                })
                    .then(response => {
                        // Booking details response status
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Booking details response
                        if (data.success) {
                            const booking = data.booking;

                            // Store the booking ID for update instead of create
                            window.updateBookingId = updateBookingId;

                            // Check if booking can be updated and handle redirection
                            const canUpdate = booking.can_update !== undefined ? booking.can_update : true;
                            const restrictions = booking.update_restrictions || [];

                            if (!canUpdate || restrictions.length > 0) {
                                handleBookingUpdateRestrictions(booking, restrictions);
                                return;
                            }

                            // Pre-fill form fields from server data
                            fillBookingForm(booking);

                            // Wait for time dropdowns to initialize, then set the times
                            setTimeout(() => {
                                console.log('Setting booking times for update booking:', booking);
                                setBookingTimes(booking);

                                // Additional fallback to ensure end time is displayed
                                setTimeout(() => {
                                    const endTimeInput = document.getElementById('end_time');
                                    const endTimeHidden = document.getElementById('end_time_value');

                                    if (endTimeInput && (!endTimeInput.value || endTimeInput.value.trim() === '')) {
                                        console.log('End time input is empty, attempting to set it again');

                                        if (window.originalEndTime) {
                                            const endTimeDisplay = convertTo12HourFormat(window.originalEndTime);
                                            endTimeInput.value = endTimeDisplay;
                                            if (endTimeHidden) {
                                                endTimeHidden.value = window.originalEndTime;
                                            }
                                            endTimeInput.setAttribute('data-time-value', window.originalEndTime);
                                            console.log('Fallback: Set end time to:', endTimeDisplay);
                                        }
                                    } else {
                                        console.log('End time input has value:', endTimeInput ? endTimeInput.value : 'input not found');
                                    }
                                }, 200);
                            }, 500);

                            // Update button text and disable until changes are made (with delay)
                            setTimeout(() => {
                                updateBookingButtonState();
                                addChangeListeners();
                            }, 1000);
                        } else {
                            console.error('Booking details error:', data.message);
                            handleBookingDetailsError(data, bookingId);
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching booking details:', error);
                        handleBookingDetailsNetworkError(error, bookingId);
                    });
            }
        }

        // Function to handle booking details errors
        function handleBookingDetailsError(data, bookingId) {
            const updateNotification = document.querySelector('.mb-4.p-4.bg-blue-50');
            const providerId = getProviderIdFromUrl();

            let title = 'Error Loading Booking';
            let message = data.message || 'Unable to load booking details.';
            let showRedirectButton = false;
            let redirectDelay = 0;

            // Handle specific error cases
            if (data.message && data.message.includes('not found')) {
                title = 'Booking Not Found';
                message = 'The booking you\'re trying to update no longer exists. It may have been cancelled or deleted.';
                showRedirectButton = true;
                redirectDelay = 3000;
            } else if (data.message && data.message.includes('Unauthorized')) {
                title = 'Access Denied';
                message = 'You don\'t have permission to view this booking. You can only update your own bookings.';
                showRedirectButton = true;
                redirectDelay = 3000;
            } else if (data.message && data.message.includes('Authentication')) {
                title = 'Login Required';
                message = 'Please log in to update your booking.';
                // Redirect to login instead of provider profile
                setTimeout(() => {
                    window.location.href = '/login?redirect=' + encodeURIComponent(window.location.href);
                }, 2000);
                return;
            } else {
                // Generic error
                showRedirectButton = true;
                redirectDelay = 5000;
            }

            showErrorNotification(title, message, showRedirectButton, redirectDelay, providerId);
        }

        // Function to handle network errors when fetching booking details
        function handleBookingDetailsNetworkError(error, bookingId) {
            const providerId = getProviderIdFromUrl();
            let title = 'Connection Error';
            let message = 'Unable to connect to the server. Please check your internet connection and try again.';

            // Handle specific network error types
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                message = 'Network connection failed. Please check your internet connection.';
            } else if (error.message.includes('timeout')) {
                message = 'Request timed out. The server is taking too long to respond.';
            } else if (error.message.includes('500')) {
                title = 'Server Error';
                message = 'The server encountered an error. Please try again in a few moments.';
            } else if (error.message.includes('404')) {
                title = 'Booking Not Found';
                message = 'The booking you\'re trying to update could not be found.';
            }

            showErrorNotification(title, message, true, 5000, providerId);
        }

        // Function to show error notifications with consistent styling
        function showErrorNotification(title, message, showRedirectButton = false, redirectDelay = 0, providerId = null) {
            const updateNotification = document.querySelector('.mb-4.p-4.bg-blue-50');

            if (updateNotification) {
                const redirectButtonHtml = showRedirectButton && providerId ? `
                    <div class="mt-3">
                        <button onclick="redirectToProviderProfile(${providerId})"
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors mr-2">
                            Make New Booking
                        </button>
                        <button onclick="window.location.reload()"
                                class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            Try Again
                        </button>
                    </div>
                ` : `
                    <div class="mt-3">
                        <button onclick="window.location.reload()"
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            Try Again
                        </button>
                    </div>
                `;

                updateNotification.innerHTML = `
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-red-800">${title}</h4>
                            <p class="text-sm text-red-600 mt-1">${message}</p>
                            ${redirectButtonHtml}
                            ${showRedirectButton && redirectDelay > 0 && providerId ? `
                                <p class="text-xs text-red-500 mt-2">
                                    You will be redirected to make a new booking in <span id="redirectCountdown">${Math.ceil(redirectDelay/1000)}</span> seconds.
                                </p>
                            ` : ''}
                        </div>
                    </div>
                `;
                updateNotification.className = 'mb-4 p-4 bg-red-50 border border-red-200 rounded-lg';
            }

            // Disable the form
            const form = document.getElementById('bookingForm');
            if (form) {
                const inputs = form.querySelectorAll('input, select, textarea, button');
                inputs.forEach(input => {
                    input.disabled = true;
                });
            }

            // Set up automatic redirection with countdown (only if providerId is available)
            if (showRedirectButton && redirectDelay > 0 && providerId) {
                let countdown = Math.ceil(redirectDelay / 1000);
                const countdownElement = document.getElementById('redirectCountdown');

                const countdownInterval = setInterval(() => {
                    countdown--;
                    if (countdownElement) {
                        countdownElement.textContent = countdown;
                    }

                    if (countdown <= 0) {
                        clearInterval(countdownInterval);
                        redirectToProviderProfile(providerId);
                    }
                }, 1000);

                // Store interval ID for potential cleanup
                window.redirectCountdownInterval = countdownInterval;
            }
        }

        // Helper function to get provider ID from URL
        function getProviderIdFromUrl() {
            const pathParts = window.location.pathname.split('/');
            const findPersonIndex = pathParts.indexOf('find-person');
            if (findPersonIndex !== -1 && pathParts[findPersonIndex + 1]) {
                return parseInt(pathParts[findPersonIndex + 1]);
            }
            return null;
        }

        // Function to handle booking update restrictions and smart redirection
        function handleBookingUpdateRestrictions(booking, restrictions) {
            const updateNotification = document.querySelector('.mb-4.p-4.bg-blue-50');
            const providerId = booking.provider_id;

            // Determine the appropriate message and action based on restrictions
            let title = 'Cannot Update Booking';
            let message = '';
            let showRedirectButton = false;
            let redirectDelay = 0;

            if (restrictions.includes('Cannot update after meeting start time')) {
                title = 'Meeting Time Has Passed';
                message = 'This booking cannot be updated as the meeting time has already started.';
                showRedirectButton = true;
                redirectDelay = 5000; // 5 seconds
            } else if (restrictions.includes('Accepted bookings cannot be updated')) {
                title = 'Booking Already Accepted';
                message = 'This booking has been accepted by the provider and cannot be modified. You can make a new booking if needed.';
                showRedirectButton = true;
                redirectDelay = 3000; // 3 seconds
            } else if (restrictions.includes('Rejected bookings cannot be updated')) {
                title = 'Booking Was Rejected';
                message = 'This booking was rejected by the provider. You can make a new booking with different details.';
                showRedirectButton = true;
                redirectDelay = 3000; // 3 seconds
            } else if (restrictions.includes('Cancelled bookings cannot be updated')) {
                title = 'Booking Was Cancelled';
                message = 'This booking has been cancelled and cannot be updated. You can make a new booking if needed.';
                showRedirectButton = true;
                redirectDelay = 3000; // 3 seconds
            } else {
                // Generic restriction message
                message = restrictions.join('. ') + '.';
                showRedirectButton = true;
                redirectDelay = 3000;
            }

            // Show the restriction message
            if (updateNotification) {
                const redirectButtonHtml = showRedirectButton ? `
                    <div class="mt-3">
                        <button onclick="redirectToProviderProfile(${providerId})"
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            Make New Booking
                        </button>
                    </div>
                ` : '';

                updateNotification.innerHTML = `
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-red-800">${title}</h4>
                            <p class="text-sm text-red-600 mt-1">${message}</p>
                            ${redirectButtonHtml}
                            ${showRedirectButton && redirectDelay > 0 ? `
                                <p class="text-xs text-red-500 mt-2">
                                    You will be redirected to make a new booking in <span id="redirectCountdown">${Math.ceil(redirectDelay/1000)}</span> seconds.
                                </p>
                            ` : ''}
                        </div>
                    </div>
                `;
                updateNotification.className = 'mb-4 p-4 bg-red-50 border border-red-200 rounded-lg';
            }

            // Disable the form
            const form = document.getElementById('bookingForm');
            if (form) {
                const inputs = form.querySelectorAll('input, select, textarea, button');
                inputs.forEach(input => {
                    input.disabled = true;
                });
            }

            // Set up automatic redirection with countdown
            if (showRedirectButton && redirectDelay > 0) {
                let countdown = Math.ceil(redirectDelay / 1000);
                const countdownElement = document.getElementById('redirectCountdown');

                const countdownInterval = setInterval(() => {
                    countdown--;
                    if (countdownElement) {
                        countdownElement.textContent = countdown;
                    }

                    if (countdown <= 0) {
                        clearInterval(countdownInterval);
                        redirectToProviderProfile(providerId);
                    }
                }, 1000);

                // Store interval ID for potential cleanup
                window.redirectCountdownInterval = countdownInterval;
            }
        }

        // Function to redirect to provider profile for new booking
        function redirectToProviderProfile(providerId) {
            if (window.redirectCountdownInterval) {
                clearInterval(window.redirectCountdownInterval);
            }

            // Remove the update_booking parameter to go to normal booking mode
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.delete('update_booking');

            // Redirect to the provider profile
            window.location.href = currentUrl.toString();
        }

        // Function to set booking times in dropdowns
        function setBookingTimes(booking) {
            const bookingDate = booking.booking_date;
            // Use actual_duration_hours for the real duration, fallback to duration_hours
            const durationHours = booking.actual_duration_hours || booking.duration_hours;

            if (bookingDate) {
                const dateObj = new Date(bookingDate);

                // Calculate start and end times
                const startHours = dateObj.getHours().toString().padStart(2, '0');
                const startMinutes = dateObj.getMinutes().toString().padStart(2, '0');
                const startTime = `${startHours}:${startMinutes}`;

                // Calculate end time first
                let endTime = null;
                if (durationHours) {
                    const endTimeObj = new Date(dateObj.getTime() + (parseFloat(durationHours) * 60 * 60 * 1000));
                    const endHours = endTimeObj.getHours().toString().padStart(2, '0');
                    const endMinutes = endTimeObj.getMinutes().toString().padStart(2, '0');
                    endTime = `${endHours}:${endMinutes}`;
                }

                // Store original times
                window.originalStartTime = startTime;
                window.originalEndTime = endTime;

                // Temporarily disable the end time update when start time changes
                window.isSettingBookingTimes = true;

                // Set start time in dropdown
                if (window.startTimeDropdown) {
                    window.startTimeDropdown.setValue(startTime);
                    console.log('Set start time to:', startTime);
                }

                // Update end time options to include the calculated end time
                if (endTime && window.endTimeDropdown) {
                    console.log('Setting end time to:', endTime);

                    // First update the options to include times after start time
                    updateEndTimeOptionsForSearchable(startTime);

                    // Then set the end time value with multiple attempts
                    setTimeout(() => {
                        if (window.endTimeDropdown) {
                            // Try setting via dropdown first
                            window.endTimeDropdown.setValue(endTime);

                            // Verify it was set correctly
                            const dropdownValue = window.endTimeDropdown.getValue();
                            console.log('End time dropdown value after setValue:', dropdownValue);

                            if (dropdownValue !== endTime) {
                                console.warn('Dropdown setValue failed, trying direct input method');

                                // Fallback: set the visible input field directly
                                const endTimeInput = document.getElementById('end_time');
                                const endTimeHidden = document.getElementById('end_time_value');
                                if (endTimeInput && endTimeHidden) {
                                    // Convert 24-hour format to 12-hour format for display
                                    const endTimeDisplay = convertTo12HourFormat(endTime);
                                    endTimeInput.value = endTimeDisplay;
                                    endTimeHidden.value = endTime;
                                    endTimeInput.setAttribute('data-time-value', endTime);
                                    console.log('End time set directly in input:', endTimeDisplay, '(', endTime, ')');
                                }
                            } else {
                                console.log('End time set successfully via dropdown');
                            }
                        }

                        // Trigger amount calculation first
                        updateTotalAmount();

                        // IMPORTANT: Delay disabling isSettingBookingTimes to prevent event listeners from clearing end time
                        setTimeout(() => {
                            window.isSettingBookingTimes = false;
                            console.log('Booking times setting completed - event listeners re-enabled');
                        }, 200);
                    }, 100);
                } else {
                    console.log('No end time to set or dropdown not available');

                    // Trigger amount calculation first
                    setTimeout(() => {
                        updateTotalAmount();

                        // Then re-enable the end time update behavior after a delay
                        setTimeout(() => {
                            window.isSettingBookingTimes = false;
                            console.log('Booking times setting completed (no end time) - event listeners re-enabled');
                        }, 200);
                    }, 100);
                }
            }
        }

        // Function to fill booking form with data
        function fillBookingForm(booking) {
            const bookingDate = booking.booking_date;
            // Use actual_duration_hours for the real duration, fallback to duration_hours
            const durationHours = booking.actual_duration_hours || booking.duration_hours;
            const location = booking.meeting_location;
            const notes = booking.notes;

            // Store original booking data for comparison
            window.originalBooking = {
                booking_date: bookingDate,
                duration_hours: durationHours,
                location: location,
                notes: notes,
                total_amount: booking.total_amount
            };

            // Set the booking date
            if (bookingDate) {
                const dateObj = new Date(bookingDate);
                const dateInput = document.getElementById('booking_date');
                if (dateInput) {
                    dateInput.value = dateObj.toISOString().split('T')[0];
                }
            }

            if (location) {
                const locationInput = document.getElementById('location');
                if (locationInput) {
                    locationInput.value = location || '';
                }
            }

            if (notes) {
                const notesInput = document.getElementById('notes');
                if (notesInput) {
                    notesInput.value = notes || '';
                }
            }

            // Trigger date change event to load available time slots and show selected time
            setTimeout(() => {
                const dateInput = document.getElementById('booking_date');
                if (dateInput && dateInput.value) {
                    // Triggering date change
                    // Trigger change event to fetch available time slots
                    dateInput.dispatchEvent(new Event('change'));

                    // Wait for time slots to load, then trigger time calculations
                    setTimeout(() => {
                        const startTimeInput = document.getElementById('start_time');
                        const endTimeInput = document.getElementById('end_time');
                        // Start and end time values

                        if (startTimeInput && endTimeInput && startTimeInput.value && endTimeInput.value) {
                            // Trigger time change events to show duration and amount
                            startTimeInput.dispatchEvent(new Event('change'));
                            endTimeInput.dispatchEvent(new Event('change'));

                            // Also trigger total amount calculation
                            if (typeof updateTotalAmount === 'function') {
                                updateTotalAmount();
                            }
                        }
                    }, 1000); // Increased wait time for time slots to load
                }
            }, 500); // Increased initial delay

            // Scroll to booking form
            setTimeout(() => {
                const bookingFormContainer = document.querySelector('.booking-form-container');
                if (bookingFormContainer) {
                    bookingFormContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }, 800);
        }

        // Optimized function to update booking button state with debouncing
        let updateBookingButtonStatePending = false;
        function updateBookingButtonState() {
            if (!window.updateBookingId || !window.originalBooking) return;

            // Debounce rapid calls
            if (updateBookingButtonStatePending) return;
            updateBookingButtonStatePending = true;

            requestAnimationFrame(() => {
                try {

            const bookNowButton = document.getElementById('bookNowButton');
            const buttonText = document.getElementById('bookNowButtonText');
            const loadingText = document.getElementById('bookNowButtonLoadingText');

            // Check if elements exist before accessing them
            if (!bookNowButton || !buttonText || !loadingText) {
                setTimeout(updateBookingButtonState, 1000);
                return;
            }

            // Get current form values with null checks
            const dateInput = document.getElementById('booking_date');
            const startTimeInput = document.getElementById('start_time');
            const endTimeInput = document.getElementById('end_time');
            const locationInput = document.getElementById('location');
            const notesInput = document.getElementById('notes');

            if (!dateInput || !startTimeInput || !endTimeInput || !locationInput || !notesInput) {
                return;
            }

            const currentDate = dateInput.value || '';
            const currentStartTime = startTimeInput.value || '';
            const currentEndTime = endTimeInput.value || '';
            const currentLocation = locationInput.value || '';
            const currentNotes = notesInput.value || '';

            // Calculate current duration and amount
            const currentActualDuration = calculateDurationForBooking(currentStartTime, currentEndTime);
            const currentBillingDuration = calculateBillingDuration(currentActualDuration);
            const hourlyRate = {{ floatval($user->hourly_rate ?? 0) }};
            const platformFeeAmount = {{ floatval(\App\Models\Setting::get('platform_fee', 0)) }};
            const baseAmount = currentBillingDuration * (hourlyRate || 0);
            const currentAmount = baseAmount + platformFeeAmount;

            // Create current booking datetime
            const currentBookingDate = currentDate && currentStartTime ? `${currentDate}T${currentStartTime}:00` : null;

            // Check if anything has changed
            const hasChanges = (
                currentBookingDate !== window.originalBooking.booking_date ||
                currentBillingDuration !== parseFloat(window.originalBooking.duration_hours) ||
                currentLocation !== (window.originalBooking.location || '') ||
                currentNotes !== (window.originalBooking.notes || '')
            );

            if (!hasChanges) {
                // No changes - disable button and hide breakdown
                bookNowButton.disabled = true;
                buttonText.textContent = 'No Changes Made';
                loadingText.textContent = 'No Changes...';
                hideUpdatePaymentBreakdown();
            } else {
                // Changes detected - enable button and show real-time payment calculation
                bookNowButton.disabled = false;
                loadingText.textContent = 'Updating Booking...';

                const originalAmount = parseFloat(window.originalBooking.total_amount);
                const paymentDifference = currentAmount - originalAmount;

                // Show real-time payment breakdown
                showUpdatePaymentBreakdown({
                    original_amount: originalAmount,
                    new_amount: currentAmount,
                    difference: paymentDifference,
                    original_duration: parseFloat(window.originalBooking.duration_hours),
                    new_duration: currentBillingDuration
                });

                if (paymentDifference > 0) {
                    // User needs to pay more - show difference amount only
                    buttonText.textContent = `Pay Difference ₹${paymentDifference.toFixed(0)}`;
                } else if (paymentDifference < 0) {
                    // User gets refund
                    buttonText.textContent = `Update (₹${Math.abs(paymentDifference).toFixed(0)} Refund)`;
                } else {
                    // Same amount
                    buttonText.textContent = 'Update Booking';
                }
            }
                } catch (error) {
                    console.error('Error updating booking button state:', error);
                } finally {
                    updateBookingButtonStatePending = false;
                }
            });
        }

        // Show real-time payment breakdown for booking updates
        function showUpdatePaymentBreakdown(breakdown) {
            // Create or update payment breakdown display
            let breakdownEl = document.getElementById('updatePaymentBreakdown');
            if (!breakdownEl) {
                breakdownEl = document.createElement('div');
                breakdownEl.id = 'updatePaymentBreakdown';
                breakdownEl.className = 'mt-4';

                // Insert after the booking form
                const bookingForm = document.querySelector('.space-y-6');
                if (bookingForm) {
                    bookingForm.appendChild(breakdownEl);
                }
            }

            const difference = breakdown.difference;
            // Use backend data if available, otherwise calculate from frontend
            const walletBalance = breakdown.wallet_balance ?? {{ auth()->user()->getWallet()->balance ?? 0 }};
            const walletUsage = breakdown.wallet_usage ?? (difference > 0 ? Math.min(walletBalance, difference) : 0);
            const onlinePayment = breakdown.online_payment_required ?? (difference > 0 ? Math.max(0, difference - walletBalance) : 0);

            // Format duration properly
            function formatDuration(duration) {
                const hours = Math.floor(duration);
                const minutes = Math.round((duration - hours) * 60);

                if (hours === 0) {
                    return `${minutes} minutes`;
                } else if (minutes === 0) {
                    return hours === 1 ? '1 hour' : `${hours} hours`;
                } else {
                    return hours === 1 ? `1 hour ${minutes} min` : `${hours} hours ${minutes} min`;
                }
            }

            let html = `
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-200">
                    <div class="flex items-center mb-3">
                        <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                        <span class="font-semibold text-gray-800">Booking Update Calculation</span>
                    </div>
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between items-center py-2 border-b border-gray-200">
                            <span class="text-gray-700">Previously booked:</span>
                            <span class="font-medium text-gray-900">${formatDuration(breakdown.original_duration)} for ₹${Math.round(breakdown.original_amount)}</span>
                        </div>
                        <div class="flex justify-between items-center py-2 border-b border-gray-200">
                            <span class="text-gray-700">Updated booking:</span>
                            <span class="font-medium text-gray-900">${formatDuration(breakdown.new_duration)} for ₹${Math.round(breakdown.new_amount)}</span>
                        </div>
            `;

            if (difference > 0) {
                html += `
                        <div class="bg-orange-50 rounded-lg p-3 border border-orange-200">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-orange-700 font-medium">💳 Additional charges:</span>
                                <span class="font-bold text-orange-700 text-lg">₹${Math.round(difference)}</span>
                            </div>
                            <div class="text-xs text-orange-600 mb-3">Payment breakdown for the difference amount:</div>
                            <div class="space-y-2 text-xs">
                                <div class="flex justify-between items-center bg-white rounded p-2">
                                    <span class="text-gray-600">💰 Current Wallet Balance:</span>
                                    <span class="font-semibold text-blue-600">₹${Math.round(walletBalance)}</span>
                                </div>`;

                if (walletUsage > 0) {
                    html += `
                                <div class="flex justify-between items-center bg-white rounded p-2">
                                    <span class="text-gray-600">💰 From Wallet:</span>
                                    <span class="font-semibold text-green-600">₹${Math.round(walletUsage)}</span>
                                </div>`;
                }

                if (onlinePayment > 0) {
                    html += `
                                <div class="flex justify-between items-center bg-white rounded p-2">
                                    <span class="text-gray-600">💳 Online Payment Required:</span>
                                    <span class="font-semibold text-orange-600">₹${Math.round(onlinePayment)}</span>
                                </div>`;
                } else {
                    html += `
                                <div class="flex justify-between items-center bg-white rounded p-2">
                                    <span class="text-gray-600">💳 Online Payment Required:</span>
                                    <span class="font-semibold text-green-600">₹0</span>
                                </div>`;
                }

                html += `
                            </div>
                        </div>
                `;
            } else if (difference < 0) {
                html += `
                        <div class="bg-green-50 rounded-lg p-3 border border-green-200">
                            <div class="flex justify-between items-center">
                                <span class="text-green-700 font-medium">💰 Credit to wallet:</span>
                                <span class="font-bold text-green-700 text-lg">₹${Math.round(Math.abs(difference))}</span>
                            </div>
                            <div class="text-xs text-green-600 mt-1">This amount will be credited to your wallet</div>
                        </div>
                `;
            } else {
                html += `
                        <div class="bg-blue-50 rounded-lg p-3 border border-blue-200">
                            <div class="flex justify-between items-center">
                                <span class="text-blue-700 font-medium">✅ No payment difference</span>
                                <span class="font-bold text-blue-700 text-lg">₹0</span>
                            </div>
                            <div class="text-xs text-blue-600 mt-1">Same amount - no additional payment needed</div>
                        </div>
                `;
            }

            html += `
                    </div>
                </div>
            `;

            breakdownEl.innerHTML = html;
            breakdownEl.classList.remove('hidden');
        }

        // Hide payment breakdown
        function hideUpdatePaymentBreakdown() {
            const breakdownEl = document.getElementById('updatePaymentBreakdown');
            if (breakdownEl) {
                breakdownEl.classList.add('hidden');
            }
        }

        // Open difference payment for booking updates
        async function openDifferencePayment(booking, differenceAmount, updateData) {
            try {
                // Get payment details for the difference amount
                const response = await fetch(`/booking/${booking.id}/difference-payment?difference_amount=${differenceAmount}`);
                const data = await response.json();

                if (data.success) {
                    // Check if wallet covers the full difference
                    if (data.payment_breakdown && !data.payment_breakdown.payment_required) {
                        processDifferencePayment(booking.id, differenceAmount);
                        return;
                    }

                    // Always open Razorpay payment dialog - no test mode bypass

                    // Real Razorpay payment for difference - open directly without modal
                    const paymentAmount = data.payment_breakdown.razorpay_amount;

                    if (paymentAmount > 0 && data.razorpay_order_id) {
                        // Open Razorpay directly without showing breakdown modal
                        const options = {
                            key: data.razorpay_key,
                            amount: Math.round(paymentAmount * 100), // Amount in paise
                            currency: "INR",
                            name: "Dating App",
                            description: `Additional Payment for Booking Update - ₹${paymentAmount}`,
                            image: "{{ asset('images/logo.png') }}",
                            order_id: data.razorpay_order_id,
                            handler: async function (response) {
                                processDifferencePayment(booking.id, differenceAmount, response);
                            },
                            prefill: {
                                name: "{{ Auth::user()->name }}",
                                email: "{{ Auth::user()->email }}",
                                contact: "{{ Auth::user()->contact_number ?? '' }}"
                            },
                            theme: {
                                color: "#EC4899"
                            },
                            modal: {
                                ondismiss: function() {
                                    showErrorMessage('Payment cancelled. Please try again to complete the booking update.');
                                }
                            }
                        };

                        const rzp = new Razorpay(options);
                        rzp.open();
                    } else {
                        // Wallet covers full amount
                        processDifferencePayment(booking.id, differenceAmount);
                    }

                } else {
                    showErrorMessage('Error initializing payment: ' + (data.message || 'Please try again.'));
                }

            } catch (error) {
                showErrorMessage('Error initializing payment: ' + error.message);
            }
        }

        // Process difference payment
        async function processDifferencePayment(bookingId, differenceAmount, razorpayResponse = null) {
            try {

                const requestBody = {
                    booking_id: bookingId,
                    difference_amount: differenceAmount
                };

                if (razorpayResponse) {
                    requestBody.razorpay_payment_id = razorpayResponse.razorpay_payment_id;
                    requestBody.razorpay_order_id = razorpayResponse.razorpay_order_id;
                    requestBody.razorpay_signature = razorpayResponse.razorpay_signature;
                }

                const response = await fetch('/booking/process-difference-payment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();

                if (data.success) {
                    showSuccessMessage('Additional payment successful! Your booking update is confirmed.');

                    // Clear the update parameters from URL
                    const newUrl = window.location.pathname;
                    window.history.replaceState({}, document.title, newUrl);

                    // Remove update notification
                    const updateNotification = document.querySelector('.mb-4.p-4.bg-blue-50');
                    if (updateNotification) {
                        updateNotification.remove();
                    }

                    // Clear the update booking ID
                    window.updateBookingId = null;

                    // Refresh the page to show updated booking status
                    setTimeout(() => {
                        window.location.href = '/notifications';
                    }, 2000);

                } else {
                    showErrorMessage(data.message || 'Payment processing failed. Please try again.');
                }

            } catch (error) {
                showErrorMessage('Payment processing failed. Please try again.');
            }
        }

        // Payment breakdown modal functions removed - now showing above Book Now button

        // Modal functions removed - payment breakdown now shows above Book Now button

        // Add event listeners to form fields for real-time change detection
        function addChangeListeners() {
            if (!window.updateBookingId || window.listenersAdded) return;

            const fields = ['booking_date', 'start_time', 'end_time', 'location', 'notes'];
            let addedListeners = 0;

            fields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.addEventListener('change', updateBookingButtonState);
                    field.addEventListener('input', updateBookingButtonState);
                    addedListeners++;
                }
            });

            if (addedListeners === fields.length) {
                window.listenersAdded = true;
            }
        }

        // Tab functionality
        let currentActiveTab = 'booking';
        let currentBookingPage = 1;
        let hasMoreBookings = true;

        // Reviews functionality
        let currentReviewPage = 1;
        let hasMoreReviews = true;
        let currentReviewTabPage = 1;
        let hasMoreReviewsTab = true;

        // Tab switching functionality
        function switchUserTab(tabName) {
            // Update active tab
            currentActiveTab = tabName;

            // Hide all tab contents
            document.querySelectorAll('.user-tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Remove active state from all tab buttons
            document.querySelectorAll('.user-tab-button').forEach(button => {
                button.classList.remove('bg-gradient-to-r', 'from-indigo-600', 'to-purple-600', 'text-white', 'shadow-lg');
                button.classList.add('bg-transparent', 'text-gray-600', 'hover:text-gray-800', 'hover:bg-gray-50');
            });

            // Show selected tab content
            const selectedContent = document.getElementById(tabName + '-content');
            if (selectedContent) {
                selectedContent.classList.remove('hidden');
            }

            // Add active state to selected tab button
            const selectedButton = document.getElementById(tabName + '-tab');
            if (selectedButton) {
                selectedButton.classList.remove('bg-transparent', 'text-gray-600', 'hover:text-gray-800', 'hover:bg-gray-50');
                selectedButton.classList.add('bg-gradient-to-r', 'from-indigo-600', 'to-purple-600', 'text-white', 'shadow-lg');
            }

            // Load content based on tab
            if (tabName === 'bookings') {
                loadUserBookings();
            } else if (tabName === 'reviews') {
                loadUserReviewsTab();
            }
        }

        // Load user bookings
        async function loadUserBookings() {
            try {
                const response = await fetch(`/bookings/user/{{ $user->id }}?page=${currentBookingPage}`);
                const data = await response.json();

                if (data.success && data.bookings && data.bookings.data && data.bookings.data.length > 0) {
                    displayUserBookings(data.bookings.data, currentBookingPage === 1);

                    // Check if there are more bookings
                    hasMoreBookings = data.bookings.current_page < data.bookings.last_page;

                    if (hasMoreBookings) {
                        document.getElementById('loadMoreBookingsContainer').classList.remove('hidden');
                    } else {
                        document.getElementById('loadMoreBookingsContainer').classList.add('hidden');
                    }
                } else if (currentBookingPage === 1) {
                    document.getElementById('userBookingsContainer').innerHTML = `
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 00-2 2"></path>
                                </svg>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-1">No Booking Found</h3>
                            <p class="text-sm text-gray-600 mb-3">You haven't made any bookings with {{ $user->name }} yet. Book your first appointment using the "Book Time" tab!</p>
                            <button onclick="switchUserTab('booking')" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors">
                                Book Now
                            </button>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error loading bookings:', error);
                document.getElementById('userBookingsContainer').innerHTML = `
                    <div class="text-center py-8 text-red-500">
                        <p>Failed to load bookings. Please try again.</p>
                    </div>
                `;
            }
        }

        function displayUserBookings(bookings, clearContainer = false) {
            const container = document.getElementById('userBookingsContainer');

            if (clearContainer) {
                container.innerHTML = '';
            }

            if (bookings.length === 0 && clearContainer) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 00-2 2"></path>
                            </svg>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-1">No Booking Found</h3>
                        <p class="text-sm text-gray-600 mb-3">You haven't made any bookings with {{ $user->name }} yet. Book your first appointment using the "Book Time" tab!</p>
                        <button onclick="switchUserTab('booking')" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors">
                            Book Now
                        </button>
                    </div>
                `;
                return;
            }

            const bookingsHtml = bookings.map(booking => `
                    <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <h4 class="font-semibold text-gray-900">
                                        ${formatDate(booking.booking_date)}
                                    </h4>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full ${getBookingStatusColor(booking.status)}">
                                        ${getBookingStatusText(booking.status)}
                                    </span>
                                    ${booking.user_rating ? `
                                        <div class="flex items-center space-x-1 ml-2">
                                            ${generateStarRating(booking.user_rating.rating)}
                                            <span class="text-xs text-gray-500">(${booking.user_rating.rating}/5)</span>
                                        </div>
                                    ` : ''}
                                </div>
                                <div class="grid grid-cols-2 gap-3 text-sm text-gray-600">
                                    <div>
                                        <span class="font-medium">Time:</span>
                                        ${formatTime12Hour(booking.booking_date)} - ${formatTime12Hour(addHours(booking.booking_date, booking.duration_hours))}
                                    </div>
                                    <div>
                                        <span class="font-medium">Duration:</span>
                                        ${booking.duration_hours}h
                                    </div>
                                    <div>
                                        <span class="font-medium">Amount:</span>
                                        ₹${booking.total_amount}
                                    </div>
                                    <div>
                                        <span class="font-medium">Location:</span>
                                        ${booking.meeting_location || 'Not specified'}
                                    </div>
                                </div>
                                ${booking.notes ? `
                                    <div class="mt-2 p-2 bg-gray-50 rounded text-xs">
                                        <span class="font-medium text-gray-700">Notes:</span>
                                        <span class="text-gray-600">${booking.notes}</span>
                                    </div>
                                ` : ''}
                                ${booking.user_rating && booking.user_rating.review_text ? `
                                    <div class="mt-2 p-2 bg-blue-50 rounded text-xs">
                                        <span class="font-medium text-blue-700">Your Review:</span>
                                        <span class="text-blue-600">${booking.user_rating.review_text}</span>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `).join('');

            container.insertAdjacentHTML('beforeend', bookingsHtml);
        }

        function loadMoreBookings() {
            currentBookingPage++;
            loadUserBookings();
        }

        // Load user reviews when page loads
        @if(\App\Helpers\FeatureHelper::isRatingReviewSystemActive() && $user->getTotalReviewsCount() > 0)
        document.addEventListener('DOMContentLoaded', function() {
            loadUserReviews();
        });
        @endif

        async function loadUserReviews() {
            try {
                const response = await fetch(`/reviews/user/{{ $user->id }}?page=${currentReviewPage}`);
                const data = await response.json();

                if (data.success && data.reviews.data.length > 0) {
                    displayUserReviews(data.reviews.data, currentReviewPage === 1);

                    // Check if there are more reviews
                    hasMoreReviews = data.reviews.current_page < data.reviews.last_page;

                    if (hasMoreReviews) {
                        document.getElementById('loadMoreReviewsContainer').classList.remove('hidden');
                    } else {
                        document.getElementById('loadMoreReviewsContainer').classList.add('hidden');
                    }
                } else if (currentReviewPage === 1) {
                    document.getElementById('userReviewsContainer').innerHTML = `
                        <div class="text-center py-8 text-gray-500">
                            <p>No reviews to display.</p>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('userReviewsContainer').innerHTML = `
                    <div class="text-center py-8 text-red-500">
                        <p>Failed to load reviews. Please try again.</p>
                    </div>
                `;
            }
        }

        function displayUserReviews(reviews, clearContainer = false) {
            const container = document.getElementById('userReviewsContainer');

            if (clearContainer) {
                container.innerHTML = '';
            }

            if (reviews.length === 0 && clearContainer) {
                container.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <p>No reviews to display.</p>
                    </div>
                `;
                return;
            }

            const reviewsHtml = reviews.map(review => `
                <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                    <div class="flex items-start space-x-4">
                        <img src="${review.is_anonymous ? 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxMiIgeT0iMTIiPgo8cGF0aCBkPSJNMTYgN0E0IDQgMCAxIDEgOCA3QTQgNCAwIDAgMSAxNiA3Wk0xMiAxNEE3IDcgMCAwIDAgNSAyMUgxOUE3IDcgMCAwIDAgMTIgMTRaIiBmaWxsPSIjNkI3MjgwIi8+Cjwvc3ZnPgo8L3N2Zz4K' : (review.reviewer.profile_picture_url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxMiIgeT0iMTIiPgo8cGF0aCBkPSJNMTYgN0E0IDQgMCAxIDEgOCA3QTQgNCAwIDAgMSAxNiA3Wk0xMiAxNEE3IDcgMCAwIDAgNSAyMUgxOUE3IDcgMCAwIDAgMTIgMTRaIiBmaWxsPSIjNkI3MjgwIi8+Cjwvc3ZnPgo8L3N2Zz4K')}"
                             alt="${review.is_anonymous ? 'Anonymous' : review.reviewer.name}"
                             class="w-12 h-12 rounded-full border-2 border-gray-200">
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <h4 class="font-semibold text-gray-900 text-lg">
                                        ${review.is_anonymous ? 'Anonymous User' : review.reviewer.name}
                                    </h4>
                                    <div class="flex items-center space-x-2 mt-1">
                                        <div class="flex">
                                            ${generateStarRating(review.rating)}
                                        </div>
                                        <span class="text-sm font-medium text-gray-700">${review.rating}/5</span>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-500">${formatDate(review.created_at)}</span>
                            </div>
                            ${review.review_text ? `
                                <div class="bg-gray-50 rounded-lg p-4 mt-3">
                                    <p class="text-gray-700 leading-relaxed">${review.review_text}</p>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `).join('');

            container.insertAdjacentHTML('beforeend', reviewsHtml);
        }

        function loadMoreReviews() {
            currentReviewPage++;
            loadUserReviews();
        }

        function loadAllUserReviews() {
            // Switch to reviews tab
            switchUserTab('reviews');
        }

        // Load user reviews for reviews tab
        async function loadUserReviewsTab() {
            try {
                const response = await fetch(`/reviews/user/{{ $user->id }}?page=${currentReviewTabPage}`);
                const data = await response.json();

                if (data.success && data.reviews.data.length > 0) {
                    displayUserReviewsTab(data.reviews.data, currentReviewTabPage === 1);

                    // Check if there are more reviews
                    hasMoreReviewsTab = data.reviews.current_page < data.reviews.last_page;

                    if (hasMoreReviewsTab) {
                        document.getElementById('loadMoreReviewsTabContainer').classList.remove('hidden');
                    } else {
                        document.getElementById('loadMoreReviewsTabContainer').classList.add('hidden');
                    }
                } else if (currentReviewTabPage === 1) {
                    document.getElementById('userReviewsTabContainer').innerHTML = `
                        <div class="text-center py-8 text-gray-500">
                            <p>No reviews to display.</p>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('userReviewsTabContainer').innerHTML = `
                    <div class="text-center py-8 text-red-500">
                        <p>Failed to load reviews. Please try again.</p>
                    </div>
                `;
            }
        }

        function displayUserReviewsTab(reviews, clearContainer = false) {
            const container = document.getElementById('userReviewsTabContainer');

            if (clearContainer) {
                container.innerHTML = '';
            }

            if (reviews.length === 0 && clearContainer) {
                container.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <p>No reviews to display.</p>
                    </div>
                `;
                return;
            }

            const reviewsHtml = reviews.map(review => `
                <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                    <div class="flex items-start space-x-4">
                        <img src="${review.is_anonymous ? 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxMiIgeT0iMTIiPgo8cGF0aCBkPSJNMTYgN0E0IDQgMCAxIDEgOCA3QTQgNCAwIDAgMSAxNiA3Wk0xMiAxNEE3IDcgMCAwIDAgNSAyMUgxOUE3IDcgMCAwIDAgMTIgMTRaIiBmaWxsPSIjNkI3MjgwIi8+Cjwvc3ZnPgo8L3N2Zz4K' : (review.reviewer.profile_picture_url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxMiIgeT0iMTIiPgo8cGF0aCBkPSJNMTYgN0E0IDQgMCAxIDEgOCA3QTQgNCAwIDAgMSAxNiA3Wk0xMiAxNEE3IDcgMCAwIDAgNSAyMUgxOUE3IDcgMCAwIDAgMTIgMTRaIiBmaWxsPSIjNkI3MjgwIi8+Cjwvc3ZnPgo8L3N2Zz4K')}"
                             alt="${review.is_anonymous ? 'Anonymous' : review.reviewer.name}"
                             class="w-12 h-12 rounded-full border-2 border-gray-200">
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <h4 class="font-semibold text-gray-900 text-lg">
                                        ${review.is_anonymous ? 'Anonymous User' : review.reviewer.name}
                                    </h4>
                                    <div class="flex items-center space-x-2 mt-1">
                                        <div class="flex">
                                            ${generateStarRating(review.rating)}
                                        </div>
                                        <span class="text-sm font-medium text-gray-700">${review.rating}/5</span>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-500">${formatDate(review.created_at)}</span>
                            </div>
                            ${review.review_text ? `
                                <div class="bg-gray-50 rounded-lg p-4 mt-3">
                                    <p class="text-gray-700 leading-relaxed">${review.review_text}</p>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `).join('');

            container.insertAdjacentHTML('beforeend', reviewsHtml);
        }

        function loadMoreReviewsTab() {
            currentReviewTabPage++;
            loadUserReviewsTab();
        }

        // Helper functions for bookings
        function getBookingStatusColor(status) {
            switch(status) {
                case 'confirmed': return 'bg-green-100 text-green-800';
                case 'pending': return 'bg-yellow-100 text-yellow-800';
                case 'cancelled': return 'bg-red-100 text-red-800';
                case 'completed': return 'bg-blue-100 text-blue-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getBookingStatusText(status) {
            switch(status) {
                case 'confirmed': return 'Confirmed';
                case 'pending': return 'Pending';
                case 'cancelled': return 'Cancelled';
                case 'completed': return 'Completed';
                default: return status;
            }
        }

        function addHours(dateString, hours) {
            const date = new Date(dateString);
            // Handle fractional hours properly by converting to milliseconds
            const hoursInMs = parseFloat(hours) * 60 * 60 * 1000;
            return new Date(date.getTime() + hoursInMs);
        }

        // Generate star rating display
        function generateStarRating(rating) {
            let stars = '';
            for (let i = 1; i <= 5; i++) {
                if (i <= rating) {
                    stars += '<svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>';
                } else {
                    stars += '<svg class="w-4 h-4 text-gray-300 fill-current" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/></svg>';
                }
            }
            return stars;
        }

        // Generate 15-minute interval time options in 12-hour format
        function generateTimeOptions() {
            const options = [];

            for (let hour = 0; hour < 24; hour++) {
                for (let minute = 0; minute < 60; minute += 15) {
                    // Convert to 12-hour format
                    const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
                    const ampm = hour < 12 ? 'AM' : 'PM';
                    const minuteStr = minute.toString().padStart(2, '0');

                    // 24-hour format for value (backend compatibility)
                    const value24 = `${hour.toString().padStart(2, '0')}:${minuteStr}`;

                    // 12-hour format for display
                    const display12 = `${hour12}:${minuteStr} ${ampm}`;

                    options.push({
                        value: value24,
                        display: display12,
                        searchText: `${hour12} ${minuteStr} ${ampm} ${hour12}:${minuteStr} ${ampm}`.toLowerCase()
                    });
                }
            }

            return options;
        }

        // Create searchable time dropdown
        function createSearchableTimeDropdown(inputId, dropdownId, availableHours = null) {
            const input = document.getElementById(inputId);
            const dropdown = document.getElementById(dropdownId);
            const hiddenInput = document.getElementById(inputId + '_value');

            // Check if all required elements exist
            if (!input || !dropdown || !hiddenInput) {
                console.error('Required elements not found for dropdown:', inputId, dropdownId);
                return {
                    setOptions: function() {},
                    setValue: function() {},
                    getValue: function() { return ''; },
                    clear: function() {}
                };
            }

            let allOptions = generateTimeOptions();
            let filteredOptions = allOptions;

            // Filter by available hours if specified
            if (availableHours) {
                // Determine if this is start time or end time dropdown
                const isStartTime = inputId.includes('start');
                filteredOptions = filterTimeOptionsByAvailability(allOptions, availableHours, isStartTime);
            }

            // Populate dropdown with options
            function populateDropdown(options) {
                dropdown.innerHTML = '';

                if (options.length === 0) {
                    dropdown.innerHTML = '<div class="px-3 py-2 text-gray-500 text-sm">No matching times</div>';
                    return;
                }

                options.forEach(option => {
                    const div = document.createElement('div');
                    div.className = 'px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm border-b border-gray-100 last:border-b-0';
                    div.textContent = option.display;
                    div.dataset.value = option.value;
                    div.dataset.display = option.display;

                    div.addEventListener('click', function() {
                        input.value = option.display;
                        hiddenInput.value = option.value;

                        // ALSO SET THE ORIGINAL INPUT NAME ATTRIBUTE FOR FORM SUBMISSION
                        input.setAttribute('data-time-value', option.value);

                        dropdown.classList.add('hidden');



                        // Trigger change event
                        input.dispatchEvent(new Event('change'));
                    });

                    dropdown.appendChild(div);
                });
            }

            // Initial population
            populateDropdown(filteredOptions);

            // Input validation and search functionality
            input.addEventListener('input', function() {
                let inputValue = this.value;
                const originalValue = inputValue;

                // Allow only valid characters for time input (numbers, colon, space, AM, PM, letters for search terms)
                inputValue = inputValue.replace(/[^0-9:APMapm\s]/g, '');

                // If input was cleaned, update the field
                if (inputValue !== originalValue) {
                    this.value = inputValue;
                }

                const searchTerm = inputValue.toLowerCase();

                if (searchTerm === '') {
                    populateDropdown(filteredOptions);
                    dropdown.classList.remove('hidden');
                    hiddenInput.value = ''; // Clear hidden value when input is empty
                    return;
                }

                // Check if input matches a valid time format first
                const exactMatch = filteredOptions.find(option =>
                    option.display.toLowerCase() === searchTerm ||
                    option.value === searchTerm ||
                    option.display.toLowerCase().replace(/\s/g, '') === searchTerm.replace(/\s/g, '')
                );

                if (exactMatch) {
                    // If exact match found, set the value immediately
                    hiddenInput.value = exactMatch.value;
                    populateDropdown([exactMatch]);
                    dropdown.classList.remove('hidden');
                    return;
                }

                // Otherwise, filter for partial matches
                const matches = filteredOptions.filter(option =>
                    option.searchText.includes(searchTerm) ||
                    option.display.toLowerCase().includes(searchTerm) ||
                    isValidTimeSearch(searchTerm, option)
                );

                populateDropdown(matches);
                dropdown.classList.remove('hidden');

                // Clear hidden value if no exact match
                hiddenInput.value = '';

                // Add visual feedback for search
                if (matches.length === 0) {
                    // Check if input looks like invalid characters
                    const hasInvalidChars = /[^0-9:APMapm\s]/.test(searchTerm);
                    if (hasInvalidChars) {
                        dropdown.innerHTML = '<div class="px-3 py-2 text-red-500 text-sm italic">⚠️ Invalid characters detected. Only numbers, colon (:), AM/PM, and time keywords allowed.</div>';
                        input.classList.add('border-red-300');
                        input.classList.remove('border-gray-200');
                    } else {
                        // Check if it's due to availability constraints
                        const availableHours = window.currentAvailableHours;
                        let message = 'No matching times found. Try "2:30 PM", "14:30", "morning", or "afternoon"';

                        if (availableHours) {
                            message += `<br><small class="text-xs">Provider available: ${convertTo12HourFormat(availableHours.start_time)} - ${convertTo12HourFormat(availableHours.end_time)}</small>`;
                        }

                        dropdown.innerHTML = `<div class="px-3 py-2 text-gray-500 text-sm italic">${message}</div>`;
                        input.classList.remove('border-red-300');
                        input.classList.add('border-gray-200');
                    }
                } else {
                    // Valid search - reset border color
                    input.classList.remove('border-red-300');
                    input.classList.add('border-gray-200');
                }
            });

            // Prevent invalid characters on keypress
            input.addEventListener('keypress', function(e) {
                const char = String.fromCharCode(e.which);
                const allowedChars = /[0-9:APMapm\s]/;

                // Allow backspace, delete, tab, escape, enter
                if ([8, 9, 27, 13, 46].indexOf(e.keyCode) !== -1 ||
                    // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                    (e.keyCode === 65 && e.ctrlKey === true) ||
                    (e.keyCode === 67 && e.ctrlKey === true) ||
                    (e.keyCode === 86 && e.ctrlKey === true) ||
                    (e.keyCode === 88 && e.ctrlKey === true)) {
                    return;
                }

                // Prevent invalid characters
                if (!allowedChars.test(char)) {
                    e.preventDefault();
                    return false;
                }
            });

            // Show dropdown on focus
            input.addEventListener('focus', function() {
                dropdown.classList.remove('hidden');
            });

            // DISABLED - Validate input when user leaves the field
            // input.addEventListener('blur', function() {
            //     setTimeout(() => {
            //         // Only validate if dropdown is hidden (user didn't click on dropdown)
            //         if (dropdown.classList.contains('hidden')) {
            //             validateAndCorrectInput();
            //         }
            //     }, 150); // Small delay to allow dropdown clicks
            // });

            // Validate input function
            function validateAndCorrectInput() {
                const currentValue = input.value.trim();

                if (currentValue === '') {
                    hiddenInput.value = '';
                    return;
                }

                // Check if current value matches any valid option
                const exactMatch = filteredOptions.find(option =>
                    option.display.toLowerCase() === currentValue.toLowerCase() ||
                    option.display.toLowerCase().replace(/\s/g, '') === currentValue.toLowerCase().replace(/\s/g, '')
                );

                if (exactMatch) {
                    // Valid selection - update display and hidden value
                    input.value = exactMatch.display;
                    hiddenInput.value = exactMatch.value;
                } else {
                    // Invalid input - clear the field
                    input.value = '';
                    hiddenInput.value = '';

                    // Show brief error message
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'absolute top-full left-0 right-0 bg-red-100 border border-red-300 text-red-700 px-3 py-2 rounded-b-lg text-sm z-50';
                    errorDiv.textContent = 'Please select a valid time from the dropdown';

                    input.parentElement.appendChild(errorDiv);

                    setTimeout(() => {
                        if (errorDiv.parentElement) {
                            errorDiv.parentElement.removeChild(errorDiv);
                        }
                    }, 3000);
                }
            }

            // Hide dropdown when clicking outside - DISABLED VALIDATION
            document.addEventListener('click', function(e) {
                if (!input.contains(e.target) && !dropdown.contains(e.target)) {
                    dropdown.classList.add('hidden');
                    // DISABLED - validateAndCorrectInput();
                }
            });

            // Keyboard navigation
            input.addEventListener('keydown', function(e) {
                const items = dropdown.querySelectorAll('div[data-value]');
                let currentIndex = Array.from(items).findIndex(item => item.classList.contains('bg-blue-100'));

                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    if (currentIndex < items.length - 1) {
                        if (currentIndex >= 0) items[currentIndex].classList.remove('bg-blue-100');
                        currentIndex++;
                        items[currentIndex].classList.add('bg-blue-100');
                        items[currentIndex].scrollIntoView({ block: 'nearest' });
                    }
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    if (currentIndex > 0) {
                        items[currentIndex].classList.remove('bg-blue-100');
                        currentIndex--;
                        items[currentIndex].classList.add('bg-blue-100');
                        items[currentIndex].scrollIntoView({ block: 'nearest' });
                    }
                } else if (e.key === 'Enter') {
                    e.preventDefault();
                    if (currentIndex >= 0 && items[currentIndex]) {
                        items[currentIndex].click();
                    }
                } else if (e.key === 'Escape') {
                    dropdown.classList.add('hidden');
                }
            });

            return {
                setOptions: function(newOptions) {
                    filteredOptions = newOptions;
                    populateDropdown(filteredOptions);

                    // Show helpful message if no options available
                    if (filteredOptions.length === 0) {
                        const availableHours = window.currentAvailableHours;
                        const message = availableHours ?
                            `No times available within provider hours (${convertTo12HourFormat(availableHours.start_time)} - ${convertTo12HourFormat(availableHours.end_time)})` :
                            'No times available for the selected start time';
                        dropdown.innerHTML = `<div class="px-3 py-2 text-gray-500 text-sm italic">${message}</div>`;
                    }
                },
                setValue: function(value) {
                    // First try to find in all options
                    let option = allOptions.find(opt => opt.value === value);

                    if (option) {
                        // Check if this option should be available based on current filters
                        const isInFilteredOptions = filteredOptions.some(opt => opt.value === value);

                        if (!isInFilteredOptions) {
                            // If the option is not in filtered options, temporarily add it
                            // This handles cases where we're setting a pre-existing booking time
                            // that might be outside current availability constraints
                            filteredOptions.push(option);
                            populateDropdown(filteredOptions);
                        }

                        input.value = option.display;
                        hiddenInput.value = option.value;
                        input.setAttribute('data-time-value', option.value);

                        // Reset border color when valid value is set
                        input.classList.remove('border-red-300');
                        input.classList.add('border-gray-200');

                        console.log(`Set ${inputId} to:`, option.display, '(', option.value, ')');
                    } else {
                        console.warn(`Time option not found for value: ${value}`);
                    }
                },
                getValue: function() {
                    return hiddenInput.value;
                },
                clear: function() {
                    input.value = '';
                    hiddenInput.value = '';
                    // Reset border color when cleared
                    input.classList.remove('border-red-300');
                    input.classList.add('border-gray-200');
                },
                isValid: function() {
                    return hiddenInput.value !== '';
                }
            };
        }

        // Filter time options based on availability and constraints
        function filterTimeOptionsByAvailability(options, availableHours, isStartTime = true) {
            if (!availableHours) return options;

            return options.filter(option => {
                const timeValue = option.value;

                if (isStartTime) {
                    // For start time: must be within availability and allow at least 15 minutes before end
                    const endTimeLimit = subtractMinutesFromTime(availableHours.end_time, 15);
                    return timeValue >= availableHours.start_time && timeValue <= endTimeLimit;
                } else {
                    // For end time: must be within availability
                    return timeValue >= availableHours.start_time && timeValue <= availableHours.end_time;
                }
            });
        }

        // Helper function to subtract minutes from time string
        function subtractMinutesFromTime(timeString, minutes) {
            if (!timeString) return timeString;

            const [hours, mins] = timeString.split(':').map(Number);
            const totalMinutes = hours * 60 + mins;
            const newTotalMinutes = totalMinutes - minutes;

            if (newTotalMinutes < 0) return '00:00';

            const newHours = Math.floor(newTotalMinutes / 60);
            const newMins = newTotalMinutes % 60;

            return `${newHours.toString().padStart(2, '0')}:${newMins.toString().padStart(2, '0')}`;
        }

        // Populate time dropdown with options (updated for availability)
        function populateTimeDropdown(selectElement, availableHours = null) {
            const allOptions = generateTimeOptions();

            // Clear existing options except the first placeholder
            selectElement.innerHTML = '<option value="">Select time</option>';

            // Filter options based on availability
            const filteredOptions = availableHours ?
                filterTimeOptionsByAvailability(allOptions, availableHours, true) :
                allOptions;

            if (filteredOptions.length === 0) {
                const noOptionElement = document.createElement('option');
                noOptionElement.value = '';
                noOptionElement.textContent = availableHours ?
                    `No times available (${convertTo12HourFormat(availableHours.start_time)} - ${convertTo12HourFormat(availableHours.end_time)})` :
                    'No available times';
                noOptionElement.disabled = true;
                selectElement.appendChild(noOptionElement);
                return;
            }

            filteredOptions.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.display;
                selectElement.appendChild(optionElement);
            });
        }

        // Validate if search term is a valid time search
        function isValidTimeSearch(searchTerm, option) {
            // Allow common time-related search terms
            const timeKeywords = ['morning', 'afternoon', 'evening', 'night', 'am', 'pm'];

            // Check if search term is a time keyword
            if (timeKeywords.some(keyword => searchTerm.includes(keyword))) {
                return option.searchText.includes(searchTerm);
            }

            // Check if search term looks like a time (contains numbers and possibly colon)
            const timePattern = /^(\d{1,2}):?(\d{0,2})\s*(am|pm)?$/i;
            const match = searchTerm.match(timePattern);

            if (match) {
                const [, hours, minutes, ampm] = match;
                const hour = parseInt(hours);
                const min = minutes ? parseInt(minutes) : 0;

                // Validate hour range
                if (hour < 1 || hour > 12) return false;

                // Validate minute range and 15-minute intervals
                if (min !== 0 && min !== 15 && min !== 30 && min !== 45) return false;

                // If validation passes, check if it matches the option
                const searchHour12 = hour;
                const searchAmPm = ampm ? ampm.toUpperCase() : '';

                return option.display.includes(`${searchHour12}:${min.toString().padStart(2, '0')}`) &&
                       (searchAmPm === '' || option.display.includes(searchAmPm));
            }

            return false;
        }

        // Convert 24-hour time to 12-hour format for display
        function convertTo12HourFormat(time24) {
            if (!time24) return '';

            const [hours, minutes] = time24.split(':').map(Number);
            const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
            const ampm = hours < 12 ? 'AM' : 'PM';
            const minuteStr = minutes.toString().padStart(2, '0');

            return `${hour12}:${minuteStr} ${ampm}`;
        }

        // Update end time options for searchable dropdown based on selected start time
        function updateEndTimeOptionsForSearchable(startTime) {
            if (!window.endTimeDropdown || !startTime) return;

            const allOptions = generateTimeOptions();

            // Get availability hours from the global variable
            const availableHours = window.currentAvailableHours || null;

            // Filter options to only show times after the start time
            let filteredOptions = allOptions.filter(option => option.value > startTime);

            // Further filter by availability hours if specified
            if (availableHours) {
                filteredOptions = filteredOptions.filter(option =>
                    option.value <= availableHours.end_time
                );
            }

            // If we're setting booking times and have an original end time, ensure it's included
            if (window.isSettingBookingTimes && window.originalEndTime) {
                const originalEndTimeOption = allOptions.find(opt => opt.value === window.originalEndTime);
                if (originalEndTimeOption && !filteredOptions.some(opt => opt.value === window.originalEndTime)) {
                    // Add the original end time to the filtered options so it can be selected
                    filteredOptions.push(originalEndTimeOption);
                    // Sort the options to maintain proper order
                    filteredOptions.sort((a, b) => a.value.localeCompare(b.value));
                    console.log('Added original end time to filtered options:', window.originalEndTime);
                }
            }

            // Update the end time dropdown with filtered options
            window.endTimeDropdown.setOptions(filteredOptions);

            // Only clear the current end time selection if we're not setting booking times
            // Also check if we have an update booking ID to be extra safe
            const urlParams = new URLSearchParams(window.location.search);
            const updateBookingId = urlParams.get('update_booking');

            if (!window.isSettingBookingTimes && !updateBookingId) {
                window.endTimeDropdown.clear();
                console.log('End time cleared by updateEndTimeOptionsForSearchable');
            } else if (window.isSettingBookingTimes || updateBookingId) {
                console.log('Skipping end time clear - setting booking times or in update mode');
            }
        }

        // Legacy function for backward compatibility
        function updateEndTimeOptions(startTime) {
            updateEndTimeOptionsForSearchable(startTime);
        }

        // Convert time string (HH:MM) to minutes for comparison
        function timeToMinutes(timeString) {
            if (!timeString) return 0;

            const [hours, minutes] = timeString.split(':').map(Number);
            return hours * 60 + minutes;
        }

        // Debug function to check time values
        function debugTimeValues() {
            if (window.startTimeDropdown && window.endTimeDropdown) {
                const startTime = window.startTimeDropdown.getValue();
                const endTime = window.endTimeDropdown.getValue();
                const startDisplay = document.getElementById('start_time').value;
                const endDisplay = document.getElementById('end_time').value;

                // Time validation logic
            }
        }

        // Validate time inputs before form submission
        function validateTimeInputs() {
            console.log('=== VALIDATION STARTED ===');
            let isValid = true;
            let errorMessage = '';

            // Get values directly from hidden inputs and dropdowns
            const startTimeHidden = document.getElementById('start_time_value');
            const endTimeHidden = document.getElementById('end_time_value');

            const startTimeFromDropdown = window.startTimeDropdown ? window.startTimeDropdown.getValue() : '';
            const endTimeFromDropdown = window.endTimeDropdown ? window.endTimeDropdown.getValue() : '';

            const startTimeFromHidden = startTimeHidden ? startTimeHidden.value : '';
            const endTimeFromHidden = endTimeHidden ? endTimeHidden.value : '';

            console.log('Start Time - Dropdown:', startTimeFromDropdown, 'Hidden:', startTimeFromHidden);
            console.log('End Time - Dropdown:', endTimeFromDropdown, 'Hidden:', endTimeFromHidden);

            // Use dropdown values as primary source
            const startTime = startTimeFromDropdown || startTimeFromHidden;
            const endTime = endTimeFromDropdown || endTimeFromHidden;

            console.log('Final values - Start:', startTime, 'End:', endTime);

            // Check start time
            if (!startTime) {
                isValid = false;
                errorMessage = 'Please select a valid start time';
                console.log('Start time validation failed');

                // Highlight start time input
                const startTimeInput = document.getElementById('start_time');
                if (startTimeInput) {
                    startTimeInput.classList.add('border-red-300');
                    startTimeInput.classList.remove('border-gray-200');
                }
            }

            // Check end time
            if (!endTime) {
                isValid = false;
                errorMessage = errorMessage ? 'Please select valid start and end times' : 'Please select a valid end time';
                console.log('End time validation failed');

                // Highlight end time input
                const endTimeInput = document.getElementById('end_time');
                if (endTimeInput) {
                    endTimeInput.classList.add('border-red-300');
                    endTimeInput.classList.remove('border-gray-200');
                }
            }

            // Check if end time is after start time
            if (isValid && startTime && endTime) {
                console.log('Checking time comparison...');

                // Convert times to minutes for accurate comparison
                const startMinutes = timeToMinutes(startTime);
                const endMinutes = timeToMinutes(endTime);

                console.log('Start Minutes:', startMinutes, 'End Minutes:', endMinutes);
                console.log('Difference:', endMinutes - startMinutes, 'minutes');

                // End time must be after start time
                if (endMinutes <= startMinutes) {
                    isValid = false;
                    errorMessage = 'End time must be after start time';
                    console.log('Time comparison failed: End time not after start time');

                    const endTimeInput = document.getElementById('end_time');
                    if (endTimeInput) {
                        endTimeInput.classList.add('border-red-300');
                        endTimeInput.classList.remove('border-gray-200');
                    }
                } else {
                    console.log('Time comparison passed: End time is after start time');
                }
            }

            // Show error message if validation fails
            if (!isValid) {
                console.log('Validation failed:', errorMessage);
                showErrorMessage(errorMessage);
            } else {
                console.log('Validation passed successfully');
            }

            console.log('=== VALIDATION ENDED ===');
            return isValid;
        }

        // Round time to nearest 15-minute interval (updated for dropdown compatibility)
        function roundToNearestQuarter(timeString) {
            if (!timeString) return '';

            const [hours, minutes] = timeString.split(':').map(Number);

            // Round minutes to nearest 15-minute interval (0, 15, 30, 45)
            let roundedMinutes = Math.round(minutes / 15) * 15;
            let adjustedHours = hours;

            // Handle minute overflow
            if (roundedMinutes >= 60) {
                adjustedHours = hours + 1;
                roundedMinutes = 0;
            }

            // Ensure 24-hour format doesn't overflow
            if (adjustedHours >= 24) {
                adjustedHours = 23;
                roundedMinutes = 45;
            }

            // Format as HH:MM (24-hour for backend compatibility)
            return String(adjustedHours).padStart(2, '0') + ':' + String(roundedMinutes).padStart(2, '0');
        }




    </script>

    <!-- Update Booking Modal -->
    <div id="updateBookingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Update Booking</h3>

                <div id="currentBookingInfo"></div>

                <form class="space-y-4">
                    <div>
                        <label for="updateBookingDate" class="block text-sm font-medium text-gray-700 mb-1">New Date</label>
                        <input type="date" id="updateBookingDate" name="booking_date"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               min="{{ date('Y-m-d') }}" required>
                    </div>

                    <div>
                        <label for="updateBookingTime" class="block text-sm font-medium text-gray-700 mb-1">New Time</label>
                        <input type="time" id="updateBookingTime" name="booking_time"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               required>
                    </div>

                    <div>
                        <label for="updateDurationHours" class="block text-sm font-medium text-gray-700 mb-1">Duration (hours)</label>
                        <select id="updateDurationHours" name="duration_hours"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required>
                            <option value="0.5">30 minutes</option>
                            <option value="1">1 hour</option>
                            <option value="1.5">1.5 hours</option>
                            <option value="2">2 hours</option>
                            <option value="2.5">2.5 hours</option>
                            <option value="3">3 hours</option>
                            <option value="4">4 hours</option>
                            <option value="5">5 hours</option>
                            <option value="6">6 hours</option>
                            <option value="8">8 hours</option>
                            <option value="10">10 hours</option>
                            <option value="12">12 hours</option>
                        </select>
                    </div>

                    <div>
                        <label for="updateLocation" class="block text-sm font-medium text-gray-700 mb-1">Meeting Location</label>
                        <input type="text" id="updateLocation" name="location"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Enter meeting location" required>
                    </div>

                    <div class="bg-blue-50 p-3 rounded-lg">
                        <div class="flex justify-between items-center text-sm">
                            <span class="text-gray-600">New Total Amount:</span>
                            <span class="font-semibold text-blue-600">₹<span id="updateTotalAmount">0</span></span>
                        </div>
                        <div class="flex justify-between items-center text-xs text-gray-500 mt-1">
                            <span>Duration:</span>
                            <span id="updateTotalHours">0 hours</span>
                        </div>
                    </div>
                </form>

                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeUpdateBookingModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md transition-colors">
                        Cancel
                    </button>
                    <button type="button" id="updateBookingBtn" onclick="submitUpdateBooking()"
                            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
                        <span id="updateBtnText">Update Booking</span>
                        <span id="updateBtnLoading" class="hidden">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Updating...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Razorpay Script -->
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>

    <!-- Report User Modal -->
    @auth
        @if(auth()->id() !== $user->id)
            <x-report-user-modal :userId="$user->id" :userName="$user->name" />
        @endif
    @endauth

</x-app-layout>
